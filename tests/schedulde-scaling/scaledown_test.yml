suite: Scaledown Test
templates: 
    - scheduled-scaling/scaledown.yaml
tests:
   
  - it: Verify APIVersion is batch/v1beta1
    values:
      - ./../../values.yaml
    set: 
      scheduledScaling.enabled: true
    asserts:
      - isAPIVersion:
            of  : batch/v1beta1 

  - it: Verify kind is CronJob
    values:
      - ./../../values.yaml
    set: 
      scheduledScaling.enabled: true
    asserts:
      - isKind:
          of  : CronJob

  - it: Verify name if early-morning-end
    values:
      - ./../../values.yaml
    set: 
      scheduledScaling.enabled: true
      scheduledScaling.scaleDown[0].name: early-morning-end
    asserts:
      - equal:
          path: metadata.name
          value: early-morning-end-scaledown    

  - it: Verify name is mid-morning-end
    values:
      - ./../../values.yaml
    set: 
      scheduledScaling.enabled: true
      scheduledScaling.scaleDown[0].name: mid-morning-end
    asserts:
      - equal:
          path: metadata.name
          value: mid-morning-end-scaledown

  - it: Verify annotations
    values:
      - ./../../values.yaml
    release:
      namespace: "esync"
    set: 
      scheduledScaling.enabled: true 
    asserts:    
      - isSubset:
          path: metadata.annotations
          content:
               checkov.io/skip1: CKV_K8S_40=Containers should run as a high UID to avoid host conflict  
               checkov.io/skip2: CKV_K8S_14=Image Tag should be fixed - not latest or blank
               checkov.io/skip3: CKV_K8S_43=Image should use digest  

  - it: Verify namespace is provided
    values:
      - ./../../values.yaml
    release:
      namespace: "esync"
    asserts:
      - matchRegex:
          path: metadata.namespace
          pattern: ^\w*$

  - it: Verify specification
    values:
      - ./../../values.yaml
    asserts:
      - isNotEmpty:
          path: spec.successfulJobsHistoryLimit
          path: spec.failedJobsHistoryLimit

  - it: Verify annotations
    values:
      - ./../../values.yaml
    release:
      namespace: "esync"
    asserts:
      - isSubset:
          path: spec.jobTemplate.spec.template.metadata.annotations
          content:
             namespace: esync
             seccomp.security.alpha.kubernetes.io/pod: "runtime/default"

  - it: Verify security context fs group is 1001
    values:
      - ./../../values.yaml
    asserts:
      - equal:
          path: spec.jobTemplate.spec.template.spec.securityContext.fsGroup
          value: 1001

  - it: Verify restart policy is on failure
    values:
      - ./../../values.yaml
    asserts:
      - equal:
          path: spec.jobTemplate.spec.template.spec.restartPolicy
          value: OnFailure

  - it: Verify automount service account token is true
    values:
      - ./../../values.yaml
    asserts:
      - equal:
          path: spec.jobTemplate.spec.template.spec.automountServiceAccountToken
          value: true

  - it: Verify container name is kubectl
    values:
      - ./../../values.yaml
    asserts:
      - equal:
          path: spec.jobTemplate.spec.template.spec.containers[0].name
          value: kubectl

  - it: Verify container is kubectl bitnami/kubectl:latest
    values:
      - ./../../values.yaml
    asserts:
      - equal:
          path: spec.jobTemplate.spec.template.spec.containers[0].image
          value: bitnami/kubectl:latest

  - it: Verify container imagepullpolicy
    values:
      - ./../../values.yaml
    asserts:
      - matchRegex:
          path: spec.jobTemplate.spec.template.spec.containers[0].imagePullPolicy
          pattern: ^(IfNotPresent|Always)$

  - it: Verify container resource provided
    values:
      - ./../../values.yaml
    asserts:
      - matchRegex:
          path: spec.jobTemplate.spec.template.spec.containers[0].resources.requests.cpu
          pattern: ^.[0-9]{1,4}.m$
      - matchRegex:
          path: spec.jobTemplate.spec.template.spec.containers[0].resources.requests.memory
          pattern: ^[0-9\.]{1,4}(Gi|Mi)$
      - matchRegex:
          path: spec.jobTemplate.spec.template.spec.containers[0].resources.limits.cpu
          pattern: ^[0-9]{1,4}m$
      - matchRegex:
          path: spec.jobTemplate.spec.template.spec.containers[0].resources.limits.memory
          pattern: ^[0-9\.]{1,4}(Gi|Mi)$

  - it: Verify container specifications are as expected
    values:
      - ./../../values.yaml
    asserts:
      - isSubset:
          path: spec.jobTemplate.spec.template.spec.containers[0].securityContext
          content: 
              privileged: false
              allowPrivilegeEscalation: false
              runAsNonRoot: true
              runAsUser: 1001
              readOnlyRootFilesystem: true 
      - isNull:
          path: spec.jobTemplate.spec.template.spec.args
      - isNull:
          path: spec.jobTemplate.spec.template.serviceAccountName