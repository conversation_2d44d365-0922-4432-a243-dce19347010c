suite: Ingress
templates:
      - ingress/ingress.yaml

tests:
       
  - it : Verify template kind is Ingress
    values:
      - ./../../values.yaml
    set: 
      ingress.enabled: true
    asserts: 
        - isKind:
            of: Ingress

  - it : Verify APIVersion is networking.k8s.io/v1beta1  for git version 1.18-0
    values:
      - ./../../values.yaml
    set: 
      ingress.enabled: true
      Capabilities.KubeVersion.GitVersion: "1.18-0"   
    asserts: 
        - isAPIVersion:
            of: networking.k8s.io/v1beta1

  - it : Verify APIVersion is networking.k8s.io/v1beta1  for git version 1.19-0
    values:
      - ./../../values.yaml
    set: 
      ingress.enabled: true
      Capabilities.KubeVersion.GitVersion: "1.19-0"   
    asserts: 
        - isAPIVersion:
            of: networking.k8s.io/v1beta1

  - it : Verify APIVersion is networking.k8s.io/v1beta1  for git version 1.14-0
    values:
      - ./../../values.yaml
    set: 
      ingress.enabled: true
      Capabilities.KubeVersion.GitVersion: "1.14-0"   
    asserts: 
        - isAPIVersion:
            of: networking.k8s.io/v1beta1

  - it : Verify name
    values:
      - ./../../values.yaml
    set: 
      ingress.enabled: true
    release:
      name: "esync"
    asserts:
       - matchRegex: 
            path: metadata.name
            pattern: ^.*-esync-server-chart$

  - it : Verify labels
    values:
      - ./../../values.yaml
    set: 
      ingress.enabled: true
    release:
      name: "esync"
    asserts:
       - isSubset:
                  path: metadata.labels
                  content:
                      app: esync-server-chart

  - it : Verify annotations is empty
    values:
      - ./../../values.yaml
    set: 
      ingress.enabled: true
    release:
      name: "esync"
    asserts:       
       - isEmpty: 
            path: metadata.annotations
  
  - it: Verify tls spec is empty when kube version is 1.18-0
    values:
      - ./../../values.yaml
    set: 
      ingress.enabled: true
      Capabilities.KubeVersion.GitVersion: "1.18-0"
      ingress.tls: []
    asserts:
        - isEmpty: 
            path: spec.tls
  
  - it: Verify classname is empty
    values:
      - ./../../values.yaml
    set: 
      ingress.enabled: true
    asserts:  
       - isEmpty:
            path: spec.ClassName

  - it: Verify tls is empty
    values:
      - ./../../values.yaml
    set: 
      ingress.enabled: true
      ingress.tls: []
    asserts:          
       - isEmpty:
            path: spec.tls
  
  - it: Verify host and path is provided
    values:
      - ./../../values.yaml
    set: 
      ingress.enabled: true
    asserts:          
       - matchRegex: 
            path: spec.rules[0].host
            pattern: ^[a-zA-Z0-9-]{1,100}.[a-zA-Z0-9-]{1,100}.[a-zA-Z0-9-]{1,100}$
       - equal:
            path: spec.rules[0].http.paths[0].path
            value: "/"

  - it: Verify path type is ImplementationSpecific
    values:
      - ./../../values.yaml
    set: 
      ingress.enabled: true
    asserts:
       - notEqual:
            path: spec.rules[0].http.paths[0].pathType
            value: "ImplementationSpecific"

  - it: Verify service name and port
    values:
      - ./../../values.yaml
    set: 
      ingress.enabled: true
    release:
      name: "esync"
    asserts:
       - matchRegex:
            path: spec.rules[0].http.paths[0].backend.serviceName
            pattern: ^.*-esync-server-chart$
       - isNotEmpty:
            path: spec.rules[0].http.paths[0].backend.servicePort