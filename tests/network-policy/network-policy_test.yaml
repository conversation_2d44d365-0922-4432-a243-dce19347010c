suite: Network-policy Test
templates:
  - network-policy/network-policy.yaml
tests:

  - it: Verify APIVersion is networking.k8s.io/v1
    set:
      networkPolicy.enabled: true
    asserts:
      - isAPIVersion:
          of: networking.k8s.io/v1 

  - it: Verify kind is of NetworkPolicy
    set:
      networkPolicy.enabled: true
    asserts:
      - isKind:
          of: NetworkPolicy

  - it: Verify name is provided
    values:
      - ./../../values.yaml
    release:
      name: "esync"
    set:
      networkPolicy.enabled: true
    asserts:
      - matchRegex:
          path: metadata.name
          pattern: ^.[a-zA-Z0-9]{1,20}-esync-server-chart$

  - it: Verify namespace is provided
    values:
      - ./../../values.yaml
    set:
      networkPolicy.enabled: true
    asserts:
      - matchRegex:
          path: metadata.namespace
          pattern: ^\w*$

  - it: Verify pod-annotations are empty
    values:
      - ./../../values.yaml
    set:
      networkPolicy.enabled: true
    asserts:
      - isEmpty:
          path: metadata.annotations

  - it: Verify labels are not empty
    values:
      - ./../../values.yaml
    set:
      networkPolicy.enabled: true
    asserts:
      - isSubset:
          path: spec.podSelector.matchLabels
          content: 
            app: esync-server-chart

  - it: Verify policy type is ingress
    values:
      - ./../../values.yaml
    set:
      networkPolicy.enabled: true
    asserts:
      - equal:
          path: spec.policyTypes[0]
          value: Ingress

  - it: Verify Ipcidr is provided
    values:
      - ./../../values.yaml
    set:
      networkPolicy.enabled: true
    asserts:
      - matchRegex:
          path: spec.ingress[0].from[0].ipBlock.cidr
          pattern: ^[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}\/[0-9]{1,3}$

  - it: Verify except subnet mask is **********/24
    values:
      - ./../../values.yaml
    set:
      networkPolicy.enabled: true
    asserts:
      - isSubset:
          path: spec.ingress[0].from[0].ipBlock
          content:
            except: [**********/24]

  - it: Verify namespace selector is app=esync-server-chart
    values:
      - ./../../values.yaml
    set:
      networkPolicy.enabled: true
    asserts:
      - isSubset:
          path: spec.ingress[0].from[1].namespaceSelector.matchLabels
          content: 
            app: esync-server-chart
            
  - it: Verify pod selector is app=esync-server-chart
    values:
      - ./../../values.yaml
    set:
      networkPolicy.enabled: true
    asserts:
      - isSubset:
          path: spec.ingress[0].from[2].podSelector.matchLabels
          content: 
            app: esync-server-chart


