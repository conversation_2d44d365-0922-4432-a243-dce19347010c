suite: eSync-Gateway Test
templates:
  - gateway/esync-gv.yaml

tests:

  - it: Verify APIVersion is networking.istio.io/v1alpha3
    set:
      istio:
        enabled: true
    asserts:
      - isAPIVersion:
          of: networking.istio.io/v1alpha3

  - it: Verify template kind is Deployment
    set:
      istio:
        enabled: true
    asserts:
      - isKind:
          of: Gateway

  - it: Verify has required documents
    values:
      - ./../../values.yaml
    hasDocuments:
          count: 3

  - it: Verify name is provided
    set:
        istio:
          enabled: true
    values:
      - ./../../values.yaml
    release:
      name: "esync"
    asserts:
      - matchRegex:
          path: metadata.name
          pattern: ^[a-zA-Z-]{1,50}(-tsp|-ui|-device)$

  - it: Verify istio is ingressgateway
    set:
      istio:
        enabled: true
    asserts:
      - equal:
          path: spec.selector.istio
          value: ingressgateway

  - it: Verify server port number is provided
    set:
      istio:
        enabled: true
    asserts:
      - isNotEmpty:
          path: spec.servers[0].port.number

  - it: Verify server port name is provided
    set:
      istio:
        enabled: true
    asserts:
      - matchRegex:
          path: spec.servers[0].port.name
          pattern: ^(http-esync-ui|http-esync-device|http-esync-tsp)$

  - it: Verify server port protocol is provided
    set:
      istio:
        enabled: true
    asserts:
      - equal:
          path: spec.servers[0].port.protocol
          value: TCP

  - it: Verify hosts is provided
    set:
      istio:
        enabled: true
    asserts:
      - isNotEmpty:
          path: spec.servers[0].hosts