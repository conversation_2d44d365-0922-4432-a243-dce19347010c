suite: Secret Test
templates:
  - secret/secret.yaml
tests:

  - it: Verify APIVersion is v1
    asserts:
      - isAPIVersion:
          of: v1

  - it: Verify template kind is Secret
    asserts:
      - isKind:
          of: Secret

  - it: Verify name is provided
    values:
      - ./../../values.yaml
    asserts:
      - matchRegex:
          path: metadata.name
          pattern: ^.[a-zA-Z0-9-]{1,30}$

  - it: Verify annotations is empty
    values:
      - ./../../values.yaml
    asserts:
      - isEmpty:
          path: metadata.annotations

  - it: Verify namespace is empty
    values:
      - ./../../values.yaml
    asserts:
      - isEmpty:
          path: metadata.namespace

  - it: Verify path type is kubernetes.io/dockerconfigjson
    values:
      - ./../../values.yaml
    asserts:
      - equal:
          path: type
          value: kubernetes.io/dockerconfigjson
          