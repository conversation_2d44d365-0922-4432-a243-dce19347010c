suite: Secret Test
templates:
  - secret/esync-secure-keys.yaml
tests:

  - it: Verify APIVersion is v1
    asserts:
      - isAPIVersion:
          of: v1

  - it: Verify template kind is Secret
    asserts:
      - isKind:
          of: Secret

  - it: Verify name is esync-secure-keys
    values:
      - ./../../values.yaml
    asserts:
      - equal:
          path: metadata.name
          value: esync-secure-keys

  - it: Verify annotations is empty
    values:
      - ./../../values.yaml
    asserts:
      - isEmpty:
          path: metadata.annotations

  - it: Verify namespace provided
    values:
      - ./../../values.yaml
    asserts:
      - matchRegex:
          path: metadata.namespace
          pattern: ^\w*$

  - it: Verify type is opaque
    values:
      - ./../../values.yaml
    asserts:
      - equal:
          path: type
          value: Opaque

  - it: Verify data is empty
    values:
      - ./../../values.yaml
    asserts:
      - isEmpty:
          path: data