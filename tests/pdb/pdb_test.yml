suite: PodDisruptionBudget Test
templates:
  - pdb/poddisruptionbudget.yaml

tests:
  - it: Verify template kind is PodDisruptionBudget
    set: 
      pdb.minAvailable : 1    
    asserts:
      - isKind:      
          of: PodDisruptionBudget

  - it: Verify api version is policy/v1beta1
    set: 
      pdb.minAvailable : 1 
    asserts:
      - isAPIVersion:
            of: policy/v1beta1

  - it: Verify name is provided
    values:
       - ./../../values.yaml
    set: 
      pdb.minAvailable : 1
    release:
      name: "esync"
    asserts:    
        - matchRegex:
            path: metadata.name
            pattern: ^\w*-esync-server-chart$ 
        - isEmpty:
            path: metadata.annotations

  - it: Verify annotations is empty
    values:
       - ./../../values.yaml
    set: 
      pdb.minAvailable : 1
    asserts:    
        - isEmpty:
            path: metadata.annotations

  - it: Verify labels passed is as expected
    values:
       - ./../../values.yaml
    set: 
      pdb.minAvailable : 1
    release:
      name: "esync"
    asserts:
        - equal:
            path: spec.minAvailable
            value: 1
        - matchRegex:
             path:  spec.selector.matchLabels.app
             pattern: ^\w*-esync-server-chart$ 

  - it: Verify pdb for 10 minimum available for above configuration
    values:
       - ./../../values.yaml
    set: 
      pdb.minAvailable : 10
    release:
      name: "esync"
    asserts:    
        - matchRegex:
            path: metadata.name
            pattern: ^\w*-esync-server-chart$
        - isEmpty:
            path: metadata.annotations   
        - equal:
            path: spec.minAvailable
            value: 10
        - matchRegex:
            path:  spec.selector.matchLabels.app
            pattern: ^\w*-esync-server-chart$

      

  
  

    
             
  
