suite: Deployment Test
templates:
  - deployment/deployment.yaml
tests:

  - it: Verify APIVersion is apps/v1
    asserts:
      - isAPIVersion:
          of: apps/v1

  - it: Verify template kind is Deployment
    asserts:
      - isKind:
          of: Deployment

  - it: Verify podAnnotations Pre-defined Key Value pairs
    values:
      - ./../../values.yaml
    asserts:
      - isSubset:
          path: metadata.annotations
          content:
            seccomp.security.alpha.kubernetes.io/pod: "runtime/default"       
            checkov.io/skip1: CKV_K8S_40=Containers should run as a high UID to avoid host conflict
            checkov.io/skip2: CKV_K8S_43=Image should use digest
            checkov.io/skip3: CKV_K8S_38=Ensure that Service Account Tokens are only mounted where necessary

  - it: Verify namespace is provided
    values:
      - ./../../values.yaml
    asserts:
      - matchRegex:
          path: metadata.namespace
          pattern: ^\w*$

  - it: Verify chartname is provided
    values:
      - ./../../values.yaml
    release:
      name: "esync"
    asserts:
      - matchRegex:
          path: metadata.name
          pattern: ^\w*-esync-server-chart$

  - it: Verify labels provided
    values:
      - ./../../values.yaml 
    release:
      name: "esync"
    asserts: 
      - isSubset:
           path: metadata.labels
           content:
              app: esync-server-chart
              app.kubernetes.io/instance: esync
              app.kubernetes.io/managed-by: Tiller
              app.kubernetes.io/name: esync-server-chart
              app.kubernetes.io/version: 2.0.0
              helm.sh/chart: esync-server-chart-2.0.0
             
  - it: Verify replicaCount is not equal to 0 if autoscaling disabled
    set:
      autoscaling.enabled: ""
    values:
      - ./../../values.yaml
    matchRegex:
      path: spec.replicas
      pattern: ^.[0-9]{1,2}$

  - it: Verify Rolling Update properties
      - ./../../values.yaml
    set:
      strategy.type: RollingUpdate
    matchRegex:
      path: spec.strategy.rollingUpdate.maxUnavailable
      pattern: ^.[0-9]{1,2}$    
      path: spec.strategy.rollingUpdate.maxSurge
      pattern: ^.[0-9]{1,2}$
    asserts:
      - equal:
         path: spec.strategy.type
         value: RollingUpdate 

#   - it: Verify if autoscaling is Recreate
#     values:
#       - ./../../values.yaml
#     set:
#       autoscaling.enabled: Recreate
#       strategy.type: Recreate
#     asserts:
#       - equal:
#           path: spec.startegy.type
#           value: Recreate
           
  - it: Verify selector matchlabels are set     
    release:
      name: "esync"
    asserts: 
      - isSubset:
           path: spec.selector.matchLabels
           content:
              app: esync-server-chart
              app.kubernetes.io/instance: esync
              app.kubernetes.io/name: esync-server-chart
  
  - it: Verify template metadata annotations are set
    values:
      - ./../../values.yaml
    asserts:
      - isSubset:
          path: spec.template.metadata.annotations
          content:
            seccomp.security.alpha.kubernetes.io/pod: "runtime/default"       
            checkov.io/skip1: CKV_K8S_40=Containers should run as a high UID to avoid host conflict
            checkov.io/skip2: CKV_K8S_43=Image should use digest
            checkov.io/skip3: CKV_K8S_38=Ensure that Service Account Tokens are only mounted where necessary

  - it: Verify template metadata labels are set
    values:
      - ./../../values.yaml
    release:
      name: "esync"
    asserts:
      - isSubset:
          path: spec.template.metadata.labels
          content:
            app: esync-server-chart
            app.kubernetes.io/instance: esync
            app.kubernetes.io/name: esync-server-chart

  - it: Verify imagepullsecrets name is esync-core
    values:
      - ./../../values.yaml
    asserts:
      - equal:
          path: spec.template.spec.imagePullSecrets[0].name
          value: esync-core 
            
  - it: Verify serviceaccount name is esync-service-account
    values:
      - ./../../values.yaml
    asserts:
      - equal:
          path: spec.template.spec.serviceAccountName
          value: esync-service-account

  - it: Verify securitycontext is empty
    values:
      - ./../../values.yaml
    asserts:
      - isEmpty:
          path: spec.template.spec.securityContext

  - it: Verify parameters for esync-db-migration init Container
    values:
      - ./../../values.yaml
    set:
      dbMigration.enabled: true
    asserts:
      - equal:
          path: spec.template.spec.initContainers[0].name
          value: esync-db-migration
      - matchRegex:
          path: spec.template.spec.initContainers[0].image
          pattern: ^excelfore\/esync:.*$
      - matchRegex:
          path: spec.template.spec.initContainers[0].imagePullPolicy
          pattern: ^(IfNotPresent|Always)$
      - equal:
          path: spec.template.spec.initContainers[0].command
          value: ["/bin/sh","-c"]  
      - equal:
          path: spec.template.spec.initContainers[0].args[0]
          value: /xl4migration/entrypoint.sh
      - isNotNull:
          path: spec.template.spec.initContainers[0].resources
      - isEmpty:
          path: spec.template.spec.initContainers[0].securityContext
      - isNotEmpty:
          path: spec.template.spec.initContainers[0].env[0].value
      - equal:
          path: spec.template.spec.initContainers[0].volumeMounts[0].name
          value: secret-mount-reload-esync-certificate
      - equal:
          path: spec.template.spec.initContainers[0].volumeMounts[0].mountPath
          value: /opt/soft/appshack/pki
      - equal:
          path: spec.template.spec.initContainers[0].volumeMounts[0].readOnly
          value: true
      - equal:
          path: spec.template.spec.initContainers[0].volumeMounts[1].name
          value: fluentd-config-map
      - equal:
          path: spec.template.spec.initContainers[0].volumeMounts[1].mountPath
          value: /fluentd/etc/fluentxl4.conf
      - equal:
          path: spec.template.spec.initContainers[0].volumeMounts[1].subPath
          value: fluentxl4.conf
      - equal:
          path: spec.template.spec.initContainers[0].volumeMounts[2].name
          value: configmap-snapstore-config                                        
      - equal:
          path: spec.template.spec.initContainers[0].volumeMounts[2].mountPath
          value: /snapstore.config
      - equal:
          path: spec.template.spec.initContainers[0].volumeMounts[2].subPath
          value: snapstore.config

  - it: Verify settings for fetch init container
    values:
      - ./../../values.yaml
    asserts:
      - equal:
          path: spec.template.spec.initContainers[1].name
          value: fetch
      - matchRegex:
          path: spec.template.spec.initContainers[1].image
          pattern: ^excelfore/esync:fluentd-jdk-handle-[0-9].[0-9]$
      - matchRegex:
          path: spec.template.spec.initContainers[0].imagePullPolicy
          pattern: ^(IfNotPresent|Always)$ 
      - equal:
          path: spec.template.spec.initContainers[1].command
          value: ["/bin/sh","-c"]  
      - isNotNull:
          path: spec.template.spec.initContainers[1].resources
      - matchRegex:
          path: spec.template.spec.initContainers[1].resources.requests.cpu
          pattern: ^.[0-9]{1,4}.m$
      - matchRegex:
          path: spec.template.spec.initContainers[1].resources.requests.memory
          pattern: ^[0-9\.]{1,4}(Gi|Mi)$
      - matchRegex:
          path: spec.template.spec.initContainers[1].resources.limits.cpu
          pattern: ^[0-9]{1,4}m$
      - matchRegex:
          path: spec.template.spec.initContainers[1].resources.limits.memory
          pattern: ^[0-9\.]{1,4}(Gi|Mi)$
      - isEmpty:
          path: spec.template.spec.initContainers[1].securityContext
      - equal:
          path: spec.template.spec.initContainers[1].args
          value: ["mkdir -p /fluentd-entrypoint-init; cp /fluentd/fluentd-jdk-handler.jar /fluentd-entrypoint-init/"]
      - equal:
          path: spec.template.spec.initContainers[1].volumeMounts[0].mountPath
          value: /fluentd-entrypoint-init
      - equal:
          path: spec.template.spec.initContainers[1].volumeMounts[0].name
          value: dump

  - it: Verify settings for fetch-jar init container
    values:
      - ./../../values.yaml
    asserts:
      - equal:
          path: spec.template.spec.initContainers[2].name
          value: fetch-jar
      - matchRegex:
          path: spec.template.spec.initContainers[2].image
          pattern: ^excelfore/esync:jmx-configuration-[0-9].[0-9]$
      - matchRegex:
          path: spec.template.spec.initContainers[2].imagePullPolicy
          pattern: ^(Always|IfNotPresent)$
      - equal:
          path: spec.template.spec.initContainers[2].command
          value: ["/bin/sh","-c"]
      - isNotNull:
          path: spec.template.spec.initContainers[2].resources
      - isEmpty:
          path: spec.template.spec.initContainers[2].securityContext
      - matchRegex:
          path: spec.template.spec.initContainers[2].resources.requests.cpu
          pattern: ^[0-9]{1,4}m$
      - matchRegex:
          path: spec.template.spec.initContainers[2].resources.requests.memory
          pattern: ^[0-9\.]{1,4}(Gi|Mi)$
      - matchRegex:
          path: spec.template.spec.initContainers[2].resources.limits.cpu
          pattern: ^[0-9]{1,4}m$
      - matchRegex:
          path: spec.template.spec.initContainers[2].resources.limits.memory
          pattern: ^[0-9\.]{1,4}(Gi|Mi)$
      - equal:
          path: spec.template.spec.initContainers[2].args
          value: ["mkdir -p /jmx-entrypoint-init; cp /jmx/jmx_prometheus_javaagent-0.12.0.jar /jmx-entrypoint-init/; cp /jmx/jmx-config.yml /jmx-entrypoint-init/"]
      - equal:
          path: spec.template.spec.initContainers[2].volumeMounts[0].mountPath
          value: /jmx-entrypoint-init
      - equal:
          path: spec.template.spec.initContainers[2].volumeMounts[0].name
          value: jmxdump

  - it: Verify settings For fluentd container
    values:
      - ./../../values.yaml
    asserts:
      - equal:
          path: spec.template.spec.containers[0].name
          value: fluentd
      - matchRegex:
          path: spec.template.spec.containers[0].image
          pattern: ^excelfore/esync:fluentd-logger-v[0-9]$
      - matchRegex:
          path: spec.template.spec.containers[0].imagePullPolicy
          pattern: ^(IfNotPresent|Always)$ 
      - equal:
          path: spec.template.spec.containers[0].command
          value: ["/bin/sh","-c"]  
      - isNotNull:
          path: spec.template.spec.containers[0].resources
      - matchRegex:
          path: spec.template.spec.containers[0].resources.requests.cpu
          pattern: ^[0-9]{1,4}m$
      - matchRegex:
          path: spec.template.spec.containers[0].resources.requests.memory
          pattern: ^[0-9\.]{1,4}(Gi|Mi)$
      - matchRegex:
          path: spec.template.spec.containers[0].resources.limits.cpu
          pattern: ^[0-9]{1,4}m$
      - matchRegex:
          path: spec.template.spec.containers[0].resources.limits.memory
          pattern: ^[0-9\.]{1,4}(Gi|Mi)$
      - isEmpty:
          path: spec.template.spec.containers[0].securityContext
      - equal:
          path: spec.template.spec.containers[0].args[0]
          value: fluentd -c /fluentd/etc/fluentxl4.conf -p /fluentd/plugins --gemfile /fluentd/Gemfile;
      - isNotEmpty:
          path: spec.template.spec.containers[0].ports[0].containerPort
      - equal:
          path: spec.template.spec.containers[0].volumeMounts[0].mountPath
          value: /fluentd/etc/fluentxl4.conf
      - equal:
          path: spec.template.spec.containers[0].volumeMounts[0].name
          value: fluentd-config-map
      - equal:
          path: spec.template.spec.containers[0].volumeMounts[0].subPath
          value: fluentxl4.conf 
      - equal:
          path: spec.template.spec.containers[0].volumeMounts[1].mountPath
          value: "/usr/local/tomcat/logs"
      - equal:
          path: spec.template.spec.containers[0].volumeMounts[1].name
          value: logs-pvc

  - it: Verify settings For esync container
    values:
      - ./../../values.yaml
    asserts:
      - equal:
          path: spec.template.spec.containers[1].name
          value: esync-server-chart
      - matchRegex:
          path: spec.template.spec.containers[1].image
          pattern: ^excelfore\/esync:.*$
      - matchRegex:
          path: spec.template.spec.containers[1].imagePullPolicy
          pattern: ^(IfNotPresent|Always)$  
      - isNotNull:
          path: spec.template.spec.containers[1].resources
      - isEmpty:
          path: spec.template.spec.containers[1].securityContext
      - isNotEmpty:
          path: spec.template.spec.containers[1].ports[0].containerPort
      - isNotEmpty:
          path: spec.template.spec.containers[1].ports[1].containerPort
      - isNotEmpty:
          path: spec.template.spec.containers[1].ports[2].containerPort
      - isNotEmpty:
          path: spec.template.spec.containers[1].ports[3].containerPort
      - isNotNull:
          path: spec.template.spec.containers[1].resources
      - matchRegex:
          path: spec.template.spec.containers[1].resources.requests.cpu
          pattern: ^[0-9]{1,4}m$
      - matchRegex:
          path: spec.template.spec.containers[1].resources.requests.memory
          pattern: ^[0-9\.]{1,4}(Gi|Mi)$
      - matchRegex:
          path: spec.template.spec.containers[1].resources.limits.cpu
          pattern: ^[0-9]{1,4}m$
      - matchRegex:
          path: spec.template.spec.containers[1].resources.limits.memory
          pattern: ^[0-9\.]{1,4}(Gi|Mi)$
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[0].name
          value: configmap-snapstore-config
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[0].mountPath
          value: /usr/local/tomcat/conf/snapstore.config
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[0].subPath
          value: snapstore.config
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[1].name
          value: configmap-snapstore-workflow-xml
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[1].mountPath
          value: /usr/local/tomcat/conf/snapstore_workflow.xml
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[1].subPath
          value: snapstore_workflow.xml
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[2].name
          value: configmap-appshack-logging-properties
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[2].mountPath
          value: /usr/local/tomcat/conf/logging.properties
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[2].subPath
          value: logging.properties
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[3].name
          value: configmap-demo-device-groups-config
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[3].mountPath
          value: /usr/local/tomcat/conf/demo_device_groups.xml
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[3].subPath
          value: demo_device_groups.xml
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[4].name
          value: configmap-quartz-xl4-properties
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[4].mountPath
          value: /usr/local/tomcat/conf/quartz_xl4.properties
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[4].subPath
          value: quartz_xl4.properties 
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[5].name
          value: configmap-server-xl4
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[5].mountPath
          value: /usr/local/tomcat/conf/server.xml
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[5].subPath
          value: server.xml
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[6].name
          value: appshack-static-config-mount
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[6].mountPath
          value: /usr/local/tomcat/conf/static/assets/sotauiv4/config.json
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[6].subPath
          value: config.json
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[7].name
          value: configmap-setenv-sh
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[7].mountPath
          value: /usr/local/tomcat/bin/setenv.sh
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[7].subPath
          value: setenv.sh
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[8].name
          value: dump
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[8].mountPath
          value: /fluentd-entrypoint-init
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[9].name
          value: jmxdump
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[9].mountPath
          value: /jmx-entrypoint-init
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[10].name
          value: secret-mount-reload-esync-certificate
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[10].mountPath
          value: /opt/soft/appshack/pki
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[10].readOnly
          value: true
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[11].name
          value: logs-pvc
      - equal:
          path: spec.template.spec.containers[1].volumeMounts[11].mountPath
          value: "/usr/local/tomcat/logs"

  - it: Verify service account volume
    values:
      - ./../../values.yaml
    set:
      serviceAccount.create: false
    asserts:
      - isNotEmpty:
          path: spec.template.spec.containers[1].volumeMounts[12].name

  - it: Verify Environment Variables for esync-chart Container --> multiple values
    values:
      - ./../../values.yaml
    asserts:
      - equal:
          path: spec.template.spec.containers[1].env[0].name
          value: MY_NODE_NAME 
      - equal:
          path: spec.template.spec.containers[1].env[0].valueFrom.fieldRef.fieldPath
          value: spec.nodeName
      - equal:
          path: spec.template.spec.containers[1].env[1].name
          value: MY_POD_NAME
      - equal:
          path: spec.template.spec.containers[1].env[1].valueFrom.fieldRef.fieldPath
          value: metadata.name
      - equal:
          path: spec.template.spec.containers[1].env[2].name
          value: MY_POD_NAMESPACE
      - equal:
          path: spec.template.spec.containers[1].env[2].valueFrom.fieldRef.fieldPath
          value: metadata.namespace
      - equal:
          path: spec.template.spec.containers[1].env[3].name
          value: MY_POD_IP
      - equal:
          path: spec.template.spec.containers[1].env[3].valueFrom.fieldRef.fieldPath
          value: status.podIP
      - equal:
          path: spec.template.spec.containers[1].env[4].name
          value: MY_POD_SERVICE_ACCOUNT
      - equal:
          path: spec.template.spec.containers[1].env[4].valueFrom.fieldRef.fieldPath
          value: spec.serviceAccountName

  # - it: should pass containers (esync-chart for env if sercretkeys not empty)
  #   values:
  #     - ./../../values.yaml
  #   asserts:
  #     - isNotEmpty:
  #         path: spec.template.spec.containers[1].env[11].name 
  #     - isEmpty:
  #         path: spec.template.spec.containers[1].env[11].valueFrom.secretKeyRef.name
  #     - isEmpty:
  #         path: spec.template.spec.containers[1].env[11].valueFrom.secretKeyRef.key

  - it: Verify Environment Variables If Secrets enabled for esync-chart Container --> multiple values (esync-chart for env service account set false)
    values:
      - ./../../values.yaml
    set:
      serviceAccount.create: false
    asserts:
      - equal:
          path: spec.template.spec.containers[1].env[5].name
          value: RDS_USER_NAME 
      - equal:
          path: spec.template.spec.containers[1].env[6].name
          value: RDS_PASSWORD
      - equal:
          path: spec.template.spec.containers[1].env[7].name
          value: AMQ_USER_NAME
      - equal:
          path: spec.template.spec.containers[1].env[8].name
          value: AMQ_PASSWORD
      - equal:
          path: spec.template.spec.containers[1].env[9].name
          value: TRUSTSTORE_PASSWORD
      - equal:
          path: spec.template.spec.containers[1].env[10].name
          value: CERTIFICATE_KEY_PASSWORD
      - equal:
          path: spec.template.spec.containers[1].env[11].name
          value: CERTIFICATE_KEYSTORE_PASSWORD
      

  - it: should pass containers (esync-chart for readiness)
    values:
      - ./../../values.yaml
    set:
      readinessProbe.enabled: true
    asserts:
      - equal:
          path: spec.template.spec.containers[1].readinessProbe.httpGet.path
          value: /snap/health?source=uO5wW3yR7nN2jG4v
      - isNotEmpty:
          path: spec.template.spec.containers[1].readinessProbe.httpGet.port
      - isNotEmpty:
          path: spec.template.spec.containers[1].readinessProbe.initialDelaySeconds
      - isNotEmpty:
          path: spec.template.spec.containers[1].readinessProbe.periodSeconds
      - isNotEmpty:
          path: spec.template.spec.containers[1].readinessProbe.timeoutSeconds
      - isNotEmpty:
          path: spec.template.spec.containers[1].readinessProbe.successThreshold
      - isNotEmpty:
          path: spec.template.spec.containers[1].readinessProbe.failureThreshold

  - it: should pass containers (esync-chart for env livenessProbe)
    values:
      - ./../../values.yaml
    set:
      livenessProbe.enabled: true
    asserts:
      - equal:
          path: spec.template.spec.containers[1].livenessProbe.httpGet.path
          value: /snap/health?source=uO5wW3yR7nN2jG4v
      - isNotEmpty:
          path: spec.template.spec.containers[1].livenessProbe.httpGet.port
      - isNotEmpty:
          path: spec.template.spec.containers[1].livenessProbe.initialDelaySeconds
      - isNotEmpty:
          path: spec.template.spec.containers[1].livenessProbe.periodSeconds
      - isNotEmpty:
          path: spec.template.spec.containers[1].livenessProbe.timeoutSeconds
      - isNotEmpty:
          path: spec.template.spec.containers[1].livenessProbe.successThreshold
      - isNotEmpty:
          path: spec.template.spec.containers[1].livenessProbe.failureThreshold

  - it: Verify nodeSelector, affinity and tolerations are empty 
    values:
      - ./../../values.yaml
    asserts:
      - isEmpty:
          path: spec.template.spec.nodeSelector
      - isEmpty:
          path: spec.template.spec.affinity
      - isEmpty:
          path: spec.template.spec.tolerations

  - it: Verify eSync required volumes
    values:
      - ./../../values.yaml
    asserts:
      - equal:
          path: spec.template.spec.volumes[0].name
          value: fluentd-config-map
      - isSubset:
          path: spec.template.spec.volumes[0].configMap
          content: 
            name: fluentd-config-map
      - isEmpty:
          path: spec.template.spec.volumes[1].emptyDir
      - equal:
          path: spec.template.spec.volumes[1].name
          value: dump
      - isEmpty:
          path: spec.template.spec.volumes[2].emptyDir
      - equal:
          path: spec.template.spec.volumes[2].name
          value: jmxdump
      - equal:
          path: spec.template.spec.volumes[3].name
          value: secret-mount-reload-esync-certificate
      - equal:
          path: spec.template.spec.volumes[3].secret.secretName
          value: esync-certificate
      - isEmpty:
          path: spec.template.spec.volumes[4].emptyDir
      - equal:
          path: spec.template.spec.volumes[4].name
          value: logs-pvc
      - equal:
          path: spec.template.spec.volumes[5].name
          value: configmap-snapstore-config
      - equal:
          path: spec.template.spec.volumes[5].configMap.name
          value: configmap-snapstore-config
      - equal:
          path: spec.template.spec.volumes[6].name
          value: configmap-snapstore-workflow-xml
      - equal:
          path: spec.template.spec.volumes[6].configMap.name
          value: configmap-snapstore-workflow-xml
      - equal:
          path: spec.template.spec.volumes[7].name
          value: configmap-appshack-logging-properties
      - equal:
          path: spec.template.spec.volumes[7].configMap.name
          value: configmap-appshack-logging-properties
      - equal:
          path: spec.template.spec.volumes[8].name
          value: configmap-demo-device-groups-config
      - equal:
          path: spec.template.spec.volumes[8].configMap.name
          value: configmap-demo-device-groups-config
      - equal:
          path: spec.template.spec.volumes[9].name
          value: configmap-quartz-xl4-properties
      - equal:
          path: spec.template.spec.volumes[9].configMap.name
          value: configmap-quartz-xl4-properties
      - equal:
          path: spec.template.spec.volumes[10].name
          value: configmap-server-xl4
      - equal:
          path: spec.template.spec.volumes[10].configMap.name
          value: configmap-server-xl4
      - equal:
          path: spec.template.spec.volumes[11].name
          value: appshack-static-config-mount
      - equal:
          path: spec.template.spec.volumes[11].configMap.name
          value: appshack-static-config-mount
      - equal:
          path: spec.template.spec.volumes[12].name
          value: configmap-setenv-sh
      - equal:
          path: spec.template.spec.volumes[12].configMap.name
          value: configmap-setenv-sh

  - it: Verify volumes parameters if secrets enabled
    values:
      - ./../../values.yaml
    set:
      serviceAccount.create: false
    asserts:
      - equal: 
          path:  spec.template.spec.volumes[13].name
          value: secrets-store-inline
      - equal: 
          path:  spec.template.spec.volumes[13].csi.driver
          value: secrets-store.csi.k8s.io
      - equal: 
          path:  spec.template.spec.volumes[13].csi.readOnly
          value: true
      - equal: 
          path:  spec.template.spec.volumes[13].csi.volumeAttributes.secretProviderClass
          value: aws-secrets
