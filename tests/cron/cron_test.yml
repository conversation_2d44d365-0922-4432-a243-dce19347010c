suite: CronJob Test
templates:
  - cron/cron.yaml
tests:

   - it: Verify APIVersion is batch/v1beta1
     asserts:
       - isAPIVersion:
           of: batch/v1beta1

   - it: Verify template kind is <PERSON><PERSON><PERSON><PERSON>
     asserts:
       - isKind:
           of:  CronJob
    
   - it: Verify metadata name is esync-psql-vaccume-analyser
     values:
       - ./../../values.yaml
     asserts:         
       - matchRegex:
           path: metadata.name
           pattern: esync-psql-vaccume-analyser

   - it: Verify cron schedule is set as */60 * * * *
     values:
       - ./../../values.yaml
     asserts:         
       - equal:
           path: spec.schedule
           value: "*/60 * * * *"

   - it: Verify container name is vaccum-analyser-container
     asserts:
        - equal:
           path: spec.jobTemplate.spec.template.spec.containers[0].name
           value: vaccum-analyser-container 

   - it: Verify conatiner image is bitnami/postgresql
     values:
          - ./../../values.yaml
     asserts:
       - equal:
           path: spec.jobTemplate.spec.template.spec.containers[0].image
           value: "bitnami/postgresql"
  
   - it: Verify imagepullpolicy is either provided/not-provided
     asserts:
       - isNotEmpty:
           path: spec.jobTemplate.spec.template.spec.containers[0].imagePullPolicy
         
   - it: Verify wheather psql command is passed for conatiner (true/false)
     asserts:
       - isNotEmpty:
           path: spec.jobTemplate.spec.template.spec.containers[0].command 

   - it: Verify username is provided as env variable (true/false)
     values:
          - ./../../values.yaml
     asserts:
       - matchRegex:
          path: spec.jobTemplate.spec.template.spec.containers[0].env[0].value
          pattern: ^.[a-zA-Z0-9]{1,15}$
   
   - it: Verify password is provided as env variable (true/false)
     values:
          - ./../../values.yaml
     asserts:
       - matchRegex:
           path: spec.jobTemplate.spec.template.spec.containers[0].env[1].value
           pattern: ^.[a-zA-Z0-9]{1,15}$

   - it: Verify database name is provided as env variable (true/false)
     values:
          - ./../../values.yaml
     asserts:   
       - matchRegex:
           path: spec.jobTemplate.spec.template.spec.containers[0].env[2].value
           pattern: ^.[a-zA-Z0-9]{1,15}$          

   - it: Verify database host is provided as env variable (true/false)
     values:
          - ./../../values.yaml
     asserts:       
       - matchRegex:
           path: spec.jobTemplate.spec.template.spec.containers[0].env[3].value
           pattern:  ^.[a-zA-Z0-9!@#$&()-`.+,/\"]{1,50}$
      
   - it: Verify restart policy is set to onFailure
     set:
       spec.jobTemplate.spec.template.spec.restartPolicy.value: OnFailure