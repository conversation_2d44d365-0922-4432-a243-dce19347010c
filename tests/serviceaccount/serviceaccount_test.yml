suite: serviceAccount
templates:
- serviceaccount/serviceaccount.yaml
tests:
  - it: Verify kind is ServiceAccount
    asserts:
      - isKind:
          of: ServiceAccount
    
  - it: Verify APIVersion is v1
    asserts:
      - isAPIVersion:
          of: v1
                    
  - it: Verify name of service account is provided
    values:
      - ./../../values.yaml
    asserts:         
        - matchRegex:
            path: metadata.name
            pattern: ^.{1,100}$
            
  - it: Verify namespsace is provided
    values:
      - ./../../values.yaml
    asserts:         
        - matchRegex:
            path: metadata.namespace
            pattern: ^\w*$
            
  - it: Verify annotations is empty
    values:
        - ./../../values.yaml
    asserts:
        - isEmpty:
            path: metadata.annotations

  - it: Vefiry labels provided
    values:
      - ./../../values.yaml 
    release:
      name: "esync"
    asserts: 
      - isSubset:
           path: metadata.labels
           content:
              app: esync-server-chart
              app.kubernetes.io/instance: esync
              app.kubernetes.io/managed-by: Tiller
              app.kubernetes.io/name: esync-server-chart
              app.kubernetes.io/version: 2.0.0
              helm.sh/chart: esync-server-chart-2.0.0