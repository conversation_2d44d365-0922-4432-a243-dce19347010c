suite: HorizontalPodAutoscaler Test
templates:
      - hpa/hpa.yaml
tests:

  - it : Verify kind is set to HorizontalPodAutoscaler
    values:
         - ./../../values.yaml     
    set: 
       autoscaling.enabled: true 
    asserts:
      - isKind:
          of: HorizontalPodAutoscaler

  - it : Verify APIVersion is set to autoscaling/v2beta1
    values:
         - ./../../values.yaml     
    set: 
       autoscaling.enabled: true 
    asserts:
      - isAPIVersion:
           of: autoscaling/v2beta1

  - it : Verify annotations are set to empty
    values:
         - ./../../values.yaml     
    set: 
       autoscaling.enabled: true 
    asserts:
      - isEmpty: 
           path: metadata.annotations

  - it : Verify metadata name
    release:
      name: "esync" 
    values:
         - ./../../values.yaml     
    set: 
       autoscaling.enabled: true 
    asserts:
      - equal: 
           path: metadata.name
           value: esync-esync-server-chart
      
  - it : Verify metadata label is set as required
    values:
         - ./../../values.yaml     
    set: 
       autoscaling.enabled: true 
    asserts:      
      - isSubset:
          path: metadata.labels
          content:
             app: esync-server-chart


  - it : Verify scale target reference api version
    values:
         - ./../../values.yaml     
    set: 
       autoscaling.enabled: true 
    asserts:
      - equal:
           path: spec.scaleTargetRef.apiVersion
           value: apps/v1

  - it : Verify scale target reference kind is Deployment
    values:
         - ./../../values.yaml     
    set: 
       autoscaling.enabled: true 
    asserts:
      - equal:
           path: spec.scaleTargetRef.kind
           value: Deployment

  - it : Verify scale target reference name
    values:
         - ./../../values.yaml     
    set: 
       autoscaling.enabled: true
    release:
      name: "esync" 
    asserts:
      - equal:
           path: spec.scaleTargetRef.name
           value: esync-esync-server-chart

  - it : Verify replica minimum and maximum is provided
    values:
         - ./../../values.yaml     
    set: 
       autoscaling.enabled: true 
    asserts:
      - isNotEmpty:
           path: spec.minReplicas
      - isNotEmpty:
           path: spec.maxReplicas

  - it : Verify metrics provided for cpu hpa
    values:
         - ./../../values.yaml     
    set: 
       autoscaling.enabled: true 
       autoscaling.targetCPUUtilizationPercentage: 30
    asserts: 
      - equal:
          path: spec.metrics[0].type
          value: Resource
      - equal:
           path: spec.metrics[0].resource.name
           value: cpu
      - equal:
           path: spec.metrics[0].resource.targetAverageUtilization
           value: 30
 
  - it : Verify metrics provided for memory hpa
    values:
         - ./../../values.yaml     
    set: 
       autoscaling.enabled: true 
       autoscaling.targetMemoryUtilizationPercentage: 30
    asserts: 
      - equal:
          path: spec.metrics[0].type
          value: Resource
      - isNotEmpty:
           path: spec.metrics[0].resource.name
      - equal:
           path: spec.metrics[0].resource.targetAverageUtilization
           value: 30
      