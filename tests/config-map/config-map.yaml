suite: Config-Map Test
templates:
    - configmaps/config-json.yaml
    - configmaps/demo_device_groups.xml.yaml
    - configmaps/fluentd-config-map.yaml
    - configmaps/logging.properties.yaml
    - configmaps/quartz_xl4.properties.yaml
    - configmaps/server.xml.yaml
    - configmaps/setenv.sh.yaml
    - configmaps/snapstore_workflow.xml.yaml
    - configmaps/snapstore-config.yaml
  
tests:

  - it: Verify APIVersion is apps/v1
    asserts:
      - isAPIVersion:
          of: v1
  
  - it: Verify template kind is Deployment
    asserts:
      - isKind:
          of: ConfigMap

  - it: Verify namespace is provided
    values:
      - ./../../values.yaml
    asserts:
      - matchRegex:
          path: metadata.namespace
          pattern: ^\w*$

  - it: Verify data is not empty
    asserts:
      - isNotEmpty:
          path: data

  - it: Verify name is not empty
    values:
      - ./../../values.yaml
    asserts:
      - isNotEmpty:
          path: metadata.name

