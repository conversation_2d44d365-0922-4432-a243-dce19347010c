suite: esync-vs Test
templates:
  - virtualservice/esync-vs.yaml

tests:

  - it: Verify APIVersion is networking.istio.io/v1alpha3
    set:
      istio:
        enabled: true
    asserts:
      - isAPIVersion:
          of: networking.istio.io/v1alpha3

  - it: Verify template kind is Deployment
    set:
      istio:
        enabled: true
    asserts:
      - isKind:
          of: VirtualService

  - it: Verify hosts is provided
    set:
      istio:
        enabled: true
    asserts:
      - isNotEmpty:
          path: spec.hosts

  - it: Verify gateway is provided
    set:
      istio:
        enabled: true
    asserts:
      - matchRegex:
          path: metadata.name
          pattern: ^[a-zA-Z-]{1,50}(-tsp|-ui|-device)$

  - it: Verify tcp match port is provided
    set:
      istio:
        enabled: true
    asserts:
      - isNotEmpty:
          path: spec.tcp[0].match[0].port

  - it: Verify tcp destination port is provided
    set:
      istio:
        enabled: true
    asserts:
      - isNotEmpty:
          path: spec.tcp[0].route[0].destination.port.number

  - it: Verify tcp destination host is provided
    set:
      istio:
        enabled: true
    asserts:
      - isNotEmpty:
          path: spec.tcp[0].route[0].destination.host