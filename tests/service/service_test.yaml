suite: Service Test
templates:
  - service/service.yaml
tests:

  - it: Verify APIVersion is v1
    asserts:
      - isAPIVersion:
          of: v1

  - it: Verify template kind is Service
    asserts:
      - isKind:
          of: Service

  - it: Verify annotation namespace provided
    values:
      - ./../../values.yaml
    asserts:
      - matchRegex:
          path: metadata.annotations.namespace
          pattern: ^\w*$

  - it: Verify namespace provided
    values:
      - ./../../values.yaml
    asserts:
      - matchRegex:
          path: metadata.namespace
          pattern: ^\w*$

  - it: Verify load balancer connection time out value provided
    values:
      - ./../../values.yaml
    asserts:
      - matchRegex:
          path: metadata.annotations.service\.beta\.kubernetes\.io/aws-load-balancer-connection-idle-timeout
          pattern: ^[0-9]{1,5}$

  - it: Verify name is provided in metadata
    values:
      - ./../../values.yaml
    release:
      name: "esync"
    asserts:
      - matchRegex:
          path: metadata.name
          pattern: ^\w*-esync-server-chart$

  - it: Verify labels is provided as expected
    values:
      - ./../../values.yaml
    release:
      name: "esync"
    asserts:
      - isSubset:
          path: metadata.labels
          content:
            app: esync-server-chart
            app.kubernetes.io/instance: esync
            app.kubernetes.io/managed-by: Tiller
            app.kubernetes.io/name: esync-server-chart
            app.kubernetes.io/version: 2.0.0
            helm.sh/chart: esync-server-chart-2.0.0

  - it: Verify service type is provided
    values:
      - ./../../values.yaml
    asserts:
      - matchRegex:
          path: spec.type
          pattern: ^(LoadBalancer|NodePort|ClusterIP)$

  - it: Verify port name and details
    values:
      - ./../../values.yaml
    asserts:
      - matchRegex:
          path: spec.ports[0].name
          pattern: ^(oma|snap|tsp|sslsso)$
      - isNotEmpty:
          path: spec.ports[0].port #(regex cant be provided as it expects value as string)
      - isNotEmpty:
          path: spec.ports[0].targetPort
      - equal:
          path: spec.ports[0].protocol
          value: TCP
      - matchRegex:
          path: spec.ports[1].name
          pattern: ^(oma|snap|tsp|sslsso)$
      - isNotEmpty:
          path: spec.ports[1].port
      - isNotEmpty:
          path: spec.ports[1].targetPort
      - equal:
          path: spec.ports[1].protocol
          value: TCP
      - matchRegex:
          path: spec.ports[2].name
          pattern: ^(oma|snap|tsp|sslsso)$
      - isNotEmpty:
          path: spec.ports[2].port
      - isNotEmpty:
          path: spec.ports[2].targetPort
      - equal:
          path: spec.ports[2].protocol
          value: TCP
      - matchRegex:
          path: spec.ports[3].name
          pattern: ^(oma|snap|tsp|sslsso)$
      - isNotEmpty:
          path: spec.ports[3].port
      - isNotEmpty:
          path: spec.ports[3].targetPort
      - equal:
          path: spec.ports[3].protocol
          value: TCP

  - it: Verify node ports if provided.
    values:
      - ./../../values.yaml
    set:
      service:
        endpoints:
          oma:
            nodePort: 30951
      service:
        endpoints:
          snap:
            nodePort: 30952
      service:
        endpoints:
          tsp:
            nodePort: 30953
      service:
        endpoints:
          sslsso:
            nodePort: 30954
    asserts:
      - equal:
            path: spec.ports[0].nodePort
            value: 30951
      - equal:
            path: spec.ports[1].nodePort
            value: 30952
      - equal:
            path: spec.ports[2].nodePort
            value: 30954
      - equal:
            path: spec.ports[3].nodePort
            value: 30953

  - it: Verify service label selector
    values:
      - ./../../values.yaml
    release:
      name: "esync"
    asserts:
      - isSubset:
          path: spec.selector
          content: 
            app: esync-server-chart
            app.kubernetes.io/instance: esync
            app.kubernetes.io/name: esync-server-chart

         