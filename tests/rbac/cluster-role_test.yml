suite: ClusterRole Test
templates: 
  - rbac/cluster-role.yaml
tests:
   
   - it: Verify APIVersion is rbac.authorization.k8s.io/v1
     asserts:
        - equal:
            path: apiVersion
            value: rbac.authorization.k8s.io/v1
    
   - it: Verify template kind is ClusterRole
     asserts:
       - equal:
            path: kind
            value: ClusterRole
  
   - it: Verify name is provided
     values:
       - ./../../values.yaml
     release:
      name: "esync"
     asserts:  
         - matchRegex:
            path: metadata.name
            pattern: ^\w*-esync-server-chart$
  
   - it: Verify pod annotation is empty
     values:
       - ./../../values.yaml
     asserts:
       - isEmpty :
               path: metadata.annotations
  
   - it: Verify rules provided are correct
     asserts:
        -  equal:
            path: rules[0].apiGroups
            value: ["", "apps"]
        - equal:
            path: rules[0].resources
            value: ["*"]
        - equal:
            path: rules[0].verbs
            value:  ["patch", "get", "watch", "list"]        
