suite: ClusterRoleBinding Test
templates: 
    - rbac/cluster-role-binding.yaml
tests:
    
    - it: Verify APIVersion is rbac.authorization.k8s.io/v1
      asserts:
        - equal:
            path: apiVersion
            value: rbac.authorization.k8s.io/v1
      
    - it: Verify template kind is ClusterRoleBinding
      asserts:
       - equal:
           path: kind
           value: ClusterRoleBinding

    - it: Verify full name is empty
      values:
        - ./../../values.yaml
      asserts:  
        - isEmpty : 
            path: metadata.fullname

    - it: Verify name is provided
      values:
        - ./../../values.yaml
      release:
        name: "esync"
      asserts:  
          - matchRegex:
              path: metadata.name
              pattern: ^\w*-esync-server-chart$
        
    - it: Verify annotation is empty
      values:
        - ./../../values.yaml
      asserts:
        - isEmpty :
            path: metadata.annotations

    - it: Verify subject is of kind ServiceAccount
      values:
        - ./../../values.yaml
      asserts:
        - equal:
            path: subjects[0].kind
            value: ServiceAccount

    - it: Verify subject name
      values:
        - ./../../values.yaml
      asserts:    
          - equal:
              path: subjects[0].name
              value: esync-service-account
    
    - it: Verify subject namespace
      values:
        - ./../../values.yaml
      asserts:    
        - matchRegex:
            path: subjects[0].namespace
            pattern: ^\w*$
      
    - it: Verify role reference kind is ClusterRole
      asserts:  
        - equal:
            path: roleRef.kind
            value: ClusterRole

    - it: Verify roleref name
      release:
        name: "esync" 
      asserts:    
        - matchRegex:
            path: roleRef.name
            pattern: ^\w*-esync-server-chart$
          
    - it: Verify role reference api group
      asserts:    
        - equal:
            path: roleRef.apiGroup
            value: rbac.authorization.k8s.io