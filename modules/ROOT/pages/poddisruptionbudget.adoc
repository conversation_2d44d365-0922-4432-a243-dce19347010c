=== Pod Disruption Budget

==== Code

[source, yaml]
apiVersion: policy/v1beta1
kind: PodDisruptionBudget
metadata:
  name: esync-esync-server-chart   
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: "esync-esync-server-chart"


==== About
A Pod Disruption Budget is a Kubernetes resource that specifies the minimum number of pods that must remain available during a disruption caused.


==== Purpose
The purpose of PDB is to in any condition there should be that minimum number of pods which should be always available. In Deployment we have replicas which will maintain that many number of pods. 

In case of disruption, A disruption, refers to when a pod needs to be killed and respawned, if such things happen then there will be PDB which will have minimum number of pods available in any case. 

In the above code, the minAvailable field is set to 1, indicating that at least one instances should be available at any given time. The selector field identifies the target pods based on the label selector app: esync-esync-server-chart.