=== Ingress

==== Code

[source, yaml]
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: esync-esync-server-chart
  labels:
    helm.sh/chart: esync-server-chart-2.0.0
    app.kubernetes.io/name: esync-server-chart
    app.kubernetes.io/instance: esync
    app: esync-server-chart
    app.kubernetes.io/version: "2.0.0"
    app.kubernetes.io/managed-by: Helm
spec:
  rules:
    - host: "esync-test.excelfore.com"
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: esync-esync-server-chart
                port:
                  number: 9080

==== About
Ingress is a resource that manages external access to services within a cluster. It provides a way to define rules for routing traffic from outside the cluster to the appropriate services inside the cluster.

==== Purpose
Instead of creating multiple services for each type, ingress gives that flexibility of creating a single ingress resource and route to respective services based on routing rules defined. 

In the above code Ingress resource named esync-esync-server-chart that directs incoming HTTP requests with paths starting with "/" to the service named esync-esync-server-chart on port number defined. Ing<PERSON> applies this routing rule specifically for requests coming to the host name defined. 

The pathType is set to ImplementationSpecific, which means the exact behavior of the path matching will depend on the Ingress controller being used.