
=== Gateway

==== Code
[source, yaml]
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: esync-esync-server-chart-ui
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 9080
      name: http-esync-ui
      protocol: TCP
    hosts:
    - esync-test.excelfore.com

[source, yaml]
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: esync-esync-server-chart-device
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 8443
      name: http-esync-device
      protocol: TCP
    hosts:
    - esync-test.excelfore.com

[source, yaml]
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: esync-esync-server-chart-tsp
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 9084
      name: http-esync-tsp
      protocol: TCP
    hosts:
    - esync-test.excelfore.com


==== About
Gateway describes a load balancer operating at the edge of the mesh receiving incoming or outgoing HTTP/TCP connections

==== Purpose
An Istio Gateway is used to manage inbound and outbound traffic to and from the service mesh. It directs the external traffic to the cluster.

In the above code there is an Istio Gateway named within meta data to handle incoming TCP traffic to the respective hosts on respective ports defined within the Istio service mesh. 

Selector indicates that this Gateway will use the Istio default implementation for handling incoming traffic (ingressgateway).

