=== Cron

==== Code
 
[source, yaml]
apiVersion: batch/v1
kind: CronJob
metadata:
  name: esync-psql-vaccume-analyser
spec:
  schedule: "*/60 * * * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: vaccum-analyser-container
            image: "bitnami/postgresql"
            imagePullPolicy: IfNotPresent
            command: [ "bin/sh", "-c", "PGPASSWORD=********  psql --host=esync-postgres-postgresql.esync.svc.cluster.local --port=5432 --username=snapstore --dbname=snapstore -c 'VACUUM ANALYZE;'" ]
            env:
              - name: POSTGRESQL_USERNAME
                value: snapstore
              - name: POSTGRESQL_PASSWORD
                value: *********
              - name: POSTGRESQL_DATABASE
                value: snapstore            
              - name: DB_HOST
                value: esync-postgres-postgresql.esync.svc.cluster.local
          restartPolicy: OnFailure

==== About
CronJob creates jobs on a repeating schedule. These CronJob are usually managed by control plane or master node of kubernetes.


==== Purpose
The purpose of this cron job is it runs for every hour and creates a new pod and in each pod using the specified image it runs the postgres command. 

When it is running postgres command there is a VACUUM ANALYZE, VACUUM removes all the dead tables and rows and ANALYZE provides up-to-date statistics. When the postgres commands is running VACUUM ANALYZE it frees up disk space while also providing updated statistics. Each pod every hour once it is brought up it does the tasks and after the completion of that task the pod terminates. 

Concurrency policy as failed refers that only after completion of one job another job should start.
