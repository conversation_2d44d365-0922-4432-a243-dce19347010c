=== Service

==== Code

[source, yaml]
apiVersion: v1
kind: Service
metadata:
  annotations:
   namespace: esync
   service.beta.kubernetes.io/aws-load-balancer-connection-idle-timeout: "4000"
  namespace: esync  
  name: esync-esync-server-chart
  labels:
    helm.sh/chart: esync-server-chart-2.0.0
    app.kubernetes.io/name: esync-server-chart
    app.kubernetes.io/instance: esync
    app: esync-server-chart
    app.kubernetes.io/version: "2.0.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: LoadBalancer
  ports:
    - port: 8443
      targetPort: 8443
      protocol: TCP
      name: oma
    - port: 9080
      targetPort: 9080
      protocol: TCP
      name: snap
    - port: 9086
      targetPort: 9086
      protocol: TCP
      name: sslsso
    - port: 9084
      targetPort: 9084
      protocol: TCP
      name: tsp      
  selector:
    app.kubernetes.io/name: esync-server-chart
    app.kubernetes.io/instance: esync
    app: esync-server-chart

==== About
For some parts of the application you may want to expose a Service onto an external IP address, one that's accessible from outside of your cluster. Service types allow you to specify what kind of Service you want to expose (clusterIP, LoadBalancer, NodePort)

==== Purpose
The purpose of service file is you want to access the application from outside world. Logging into the cluster and accessing is limited. But for accessing application from external traffic, service file helps. 

In the above code the service type provided is Load Balancer, so the service file selects the pods based on the selector mentioned and service listens to the specified port and forwards the traffic to the respective target port based on protocol on the selected pod.