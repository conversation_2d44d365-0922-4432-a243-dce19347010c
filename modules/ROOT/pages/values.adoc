= Esync Server Chart
:doctype: manual
:toc: left
:toclevels: 5

=== Values file

values.yaml file contains configuration settings for the chart. This includes parameters like the image repository to pull, Database configurations and many more.

==== In Detail

==== About Kubectl 
The Kubernetes command-line tool, kubectl, allows you to run commands against Kubernetes clusters. You can use kubectl to deploy applications, inspect and manage cluster resources, and view logs.

==== About  helm 
Helm is a tool that streamlines installing and managing Kubernetes applications. It is equivalent like apt/yum/homebrew for Kubernetes.

=== Replica Count

==== Code

[source, yaml]
replicaCount: 1

==== About
The replica count is a parameter that specifies the number of identical pod instances (replicas) of a esync application that should be running in the cluster at any given time.


==== Purpose
The purpose of setting a replica count is to ensure that that many number of pods are availabile . By having replicas of a pod, Kubernetes can distribute the load across these replicas and ensuring that the pod remains available to handle requests.

=== Liveness Probe

==== Code

[source, yaml]
livenessProbe:
    ssl: true
    httpGet:
      path: /
      port: 9445
    initialDelaySeconds: 300
    periodSeconds: 180
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 6


==== About
A liveness probe is a feature that checks the health of a running container to determine if it should be restarted.

==== Purpose
The purpose of a liveness probe is to check if the pod is healthy or not by repeatedly conducting checks. 

In the above code liveness probe does an http check for the endpoint "/" on container having port 9445. Before checking for first check or probe, Liveness will wait for initialDelaySeconds. Once the check is done between successive check it will wait for periodSeconds mentioned. 

If the check fails liveness probe waits for timeoutSeconds before declaring the check as failed. If the failureThreshold reaches by consecutive number then livness considers it as failed probe and declares the container as unhealthy. 

SuccessThreshold gives number of attempts before declaring the pod as healthy. 

If the liveness probe fails, Kubernetes kills the container and will restart it based on respective parameters.

=== Readiness Probe

==== Code

[source, yaml]
readinessProbe:
    ssl: true
    httpGet:
      path: /
      port: 9445
    initialDelaySeconds: 5
    periodSeconds: 60
    timeoutSeconds: 300
    successThreshold: 6
    failureThreshold: 1


==== About
A readiness probe is a feature that checks whether a container is ready to start accepting traffic.

==== Purpose
The purpose of a readiness probe is to ensure that Kubernetes only directs traffic to containers that are fully initialized and ready to handle requests. 

In the above code readiness probe does an http check for the endpoint "/" on container having port 9445. Before checking for first check or probe readiness will wait for initialDelaySeconds. Once the check is done between successive check it will wait for periodSeconds mentioned. 

If the check fails readiness probe waits for timeoutSeconds before declaring the check as failed. If the failureThreshold reaches by consecutive number then readiness considers it as failed probe and declares the container as unhealthy. 

SuccessThreshold gives number of attempts before declaring the pod as sucess and ready to accept traffic. 

If the readiness probe fails, Kubernetes removes the specific pod, so that it no longer receives traffic.


=== Database Migration
Requirement Type : MANDATORY 

==== Code

[source, yaml]
dbMigration:
  enabled: true
  image:
    repository: excelfore/esync
    pullPolicy: IfNotPresent
    tag: core-master-latest-migration-all


==== About
Database migration is the process of transferring data from one database to another, upgrading to a new database version.

==== Purpose
If enabled, DB Migration pulls the specified eSync image, which contains the migration scripts. The migration scripts is executed inside the container which connects to the database and perform the required set of operations and upgrades to a newer version.

=== Image
Requirement Type : MANDATORY

==== Code

[source, yaml]
image:
  repository: excelfore/esync
  pullPolicy: IfNotPresent
  tag: core-master-latest #esync-release-4.2225-RC1 #


==== About
Image refers to a packaged an application, including its code, dependencies, libraries, and configurations, necessary for running the application in a specific environment.

==== Purpose
The purpose of an image is to provide a way to deploy and run applications across different environments. Images have all the necessary components and settings, so that applications behave correctly.

=== Cron
Requirement Type : MANDATORY

==== Code

[source, yaml]
cron:
  enabled: true
  cron_schedule: "*/60 * * * *"
  cron_image: bitnami/postgresql
  concurrencyPolicy: Forbid


==== About
Cron allows users to schedule tasks to run automatically at specified intervals, such as daily, weekly, or monthly. 

==== Purpose
Cron job schedules task which runs periodically at specified times with the specified eSync image. 

In the above code, if enabled, for every hour the new pod is created using the specified image and after performing the certain task the pod is terminated and Concurrency policy as failed refers that only after completion of job another job should task.

=== AWS RDS 
Requirement Type : MANDATORY

==== Code

[source, yaml]
rds:
  rds_db_port: 5432
  rds_db_name: snapstore
  rds_db_user_name: snapstore 
  rds_db_password: *********
  pool_max_active: 10
  pool_min_idle: 10
  pool_max_idle: 10
  rds_db_url: esync-postgres-postgresql.esync.svc.cluster.local


==== About
Amazon Relational Database Service (Amazon RDS) is a collection of managed services that makes it simple to set up, operate, and scale databases in the cloud.

==== Purpose 
AWS RDS Postgresql is used as database where all the data is being stored. 

rds_db_url: endpoint of rds instance with rds_db_port is used to connect to the instance. 

rds_db_username and rds_db_password : credentials requried to connect to the database.

rds_db_max_active: specifies maximum amount of connections the database pool is allowed to create. 

rds_db_min_idle: refers that database pool will not remove idle connections if the total amount of idle connections is less than this value. 

rds_db_max_idle: refers the database pool will force an idle connection to be closed if the total amount of idle connection is at maximum.


=== Redis
Requirement Type : MANDATORY

==== Code

[source, yaml]
redis:
  enabled: true
  runAsContainer: true
  resources: {}
  direct: true 
  endpoint_url: localhost #redis_endpoint_url_value #elastic-cache-g-d1468.brtwer.ng.0001.use1.cache.amazonaws.com
  port: 6379


==== About
Redis is an open-source, in-memory data structure store used as a database, cache, and message broker. Redis provides high performance and scalability, making it suitable for real-time applications.

==== Purpose
If enabled, Redis is used to cache data, enabling quick retrieval of data and reducing the load on the database. In the above code, if enabled, it is specified that redis will run as a container. endpoint_url followed by port is used to connect to redis.

=== Kubernetes Service

==== Code

[source, yaml]
service:
  internal: false
  awsLoadBalancerSslCert: 
  annotations: {}
  loadBalancerTimeOutInSeconds: 4000
  type: LoadBalancer  #LoadBalancer
  endpoints:
    oma:
      port: 8443
      targetPort: 8443
      protocol: TCP
      nodePort:
    snap:
      port: 9080
      targetPort: 9080
      protocol: TCP
      nodePort:
    tsp:
      port: 9084
      targetPort: 9084
      protocol: TCP
      nodePort:
    sslsso:
      port: 9086
      targetPort: 9086
      protocol: TCP
      nodePort:


==== About
In Kubernetes, a Service is a way to expose your application to the network, making it easy for other applications or users to connect to it. Types like ClusterIP, NodePort, and LoadBalancer help manage how the service is accessed, whether from within the cluster or from outside.

==== Purpose
The service is designed for outside traffic to communicate with the eSync Server using service type defined. In the above code the service type provided is Load Balancer. Endpoints refers to service listening to the specified ports mentioned and forwards the traffic to the respective target ports mentioned based on protocol.

loadBalancerTimeoutInSeconds refers to the maximum amount of time that a load balancer will wait for a response from a server. 


=== Startup Probe

==== Code

[source, yaml]

startupProbe:
  enabled: false
  exec:
    command: ["ls"]
  initialDelaySeconds: 0
  periodSeconds: 40
  timeoutSeconds: 1
  failureThreshold: 6
  successThreshold: 1


==== About
A startup probe is a Kubernetes feature that determines the readiness of a container before it receives traffic. It differs from readiness and liveness probes in that it checks for the initial readiness of a container during its startup phase, ensuring applications are fully ready to serve requests before being added to load balancing.

==== Purpose
The purpose of a startup probe is to delay the Kubernetes service from directing traffic to a eSync container until it confirms the container's readiness. 

In the above code, if enabled, Before checking for first check or probe startup will wait for initialDelaySeconds. Once the check is done between successive check it will wait for periodSeconds mentioned. 

If the check fails liveness probe waits for timeoutSeconds before declaring the check as failed. If the failureThreshold reaches by consecutive number then startup considers it as failed probe and declares the container as unhealthy. 

SuccessThreshold gives number of attempts before declaring the pod as success.

=== Ingress

==== Code

[source, yaml]
ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: esync-test.excelfore.com
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []

==== About
In Kubernetes, an Ingress is an API object that manages external access to services in a cluster. It acts as a layer 7 (application layer) load balancer, routing traffic based on rules defined by the user.

==== Purpose
To allow external traffic to the cluster by defining rules such as host and path-based routing ingress manages routing rules for HTTP and HTTPS. 

In the above code, if enabled, ingress resource allows access to services within the cluster using a specific host (esync-test.excelfore.com) and path (/). 

The pathType is set to ImplementationSpecific, which means the exact behavior of the path matching will depend on the Ingress controller being used.


=== Resources


==== Code

[source, yaml]
resources:
  enabled: false
  fluentd:
    cpu: 500m
    memory: 500Mi
  eSync:
    cpu: 1000m
    memory: 2Gi



==== About
The resources section refers to the configuration settings that specify the compute requirements (CPU and memory) for containers running within pods.

==== Purpose
The purpose of defining resources (CPU and memory)  is to ensure efficient allocation and management of resources for containers. By specifying resource requests and limits, Kubernetes optimizes resource utilization. In the above code, if enabled, for fluentd and esync respective memory (megaBytes) and CPU (millicores) are allocated. So that there is efficient memory utlisation and CPU's to the resources.

=== Autoscaling


==== Code

[source, yaml]

autoscaling:
  enabled: false
  minReplicas: 1
  annotations: {}
  maxReplicas: 100
  targetCPUUtilizationPercentage: 30


==== About
Autoscaling is a feature that automatically adjusts the number of replicas (pods) in a deployment based on observed metrics such as CPU utilization or custom metrics.

==== Purpose
The purpose of autoscaling is to ensure that the applications can handle varying levels of traffic and workload efficiently by automatically scaling the number of pods based on predefined metrics. 

minReplicas: The lower limit for the number of replicas to which the autoscaler can scale down. 

maxReplicas: The upper limit which the autoscaler can go. 

In the above code, if enabled, whenever the CPU utlisation crosses the mentioned target then increase the number of replicas but when the CPU utlisation is below the target mentioned then decrease the replica.  


=== Image Credentials

==== Code

[source, yaml]

imageCredentials:
  registry: docker.io
  username: **********
  password: **********
  annotations: {}


==== About
Image credentials refer to authentication information required to pull container images from private registries.

==== Purpose
The purpose of image credentials is to securely authenticate and authorize access to private container registries where application images are stored. It is used when kubernetes container runtime wants to pull images from private Docker registries.

=== Image Pull Secrets


==== Code

[source, yaml]

imagePullSecrets:
  - name: esync-core
imageSecretName: esync-core


==== About
Image Pull Secrets are Objects used to store credentials required to authenticate with private container image registries.

==== Purpose
The above created image credentials is stored in the image pull secrets. By associating Image Pull Secrets with pods or service accounts, it ensures that only authorized entities can pull and use images from private registries.

=== Config Map Reload

==== Code

[source, yaml]
configmapReload:
  name: configmap-reload
  extraConfigmapMounts: []


==== About
ConfigMapReload is a feature which does automatic reloading of applications or components when changes are made to the ConfigMaps.

==== Purpose
The purpose of ConfigMapReload is to ensure that any changes to the config map are reflected in the eSync application using it without requiring a pod restart.

=== Secret Mount Reload

==== Code

[source, yaml]

secretMountReload:
  name: secret-mount-reload
  extraSecretMounts:
    - name: esync-certificate
      mountPath: /opt/soft/appshack/pki
      secretName: esync-certificate
      readOnly: true


==== About
SecretMountReload is that facilitates automatic reloading of applications or components when changes are made to the Secrets.


==== Purpose
The purpose of SecretMountReload is to ensure that any changes to the secret are reflected in the eSync application using it without requiring a pod restart.

name: Name of secret.

mountPath: Path where the secret is mounted.

secretName: Name of secrets where the secrets are stored.

readOnly: Defines only read operation.

=== Network Policy

==== Code

[source, yaml]

networkPolicy:
  enabled: false
  annotations: {}
  ipBlock:
    cidr: "0.0.0.0/0"
    except_cidr: ["**********/24"]
  namespaceSelector:
    - name: app
      value: esync-server-chart
  podSelector:
    - name: app
      value: esync-server-chart


==== About
NetworkPolicy allows to define rules governing network communication between pods and external endpoints, enhancing security and control within clusters.

==== Purpose
The purpose of NetworkPolicy is to enforce strict network rules, ensuring that only authorized traffic flows between pods based on defined policies. 

In the above code, if enabled, The podSelector field selects pods and determines which pods the policy applies to. In this case, the NetworkPolicy targets pods labeled with name esync-server-chart. 

The above section defines to allow all the incoming traffic except the ip block defined above and allow traffic from all the pods from namespace esync-server-chart and from the pod esync-server-chart.


=== Scheduled Scaling

==== Code

[source, yaml]
scheduledScaling:
  annotations: 
    seccomp.security.alpha.kubernetes.io/pod: "runtime/default"
  enabled: false
  scaleUp:
    - time: "0 1 * * 1-5"
      replica: 5
      name: early-morning-start
    - time: "0 10 * * 1-5"
      replica: 5
      name: mid-morning-start
  scaleDown:
    - time: "0 2 * * 1-5"
      replica: 1
      name: early-morning-end
    - time: "0 12 * * 1-5"
      replica: 1    
      name: mid-morning-end



==== About
Scheduled scaling automatically adjusts the number of instances based on predefined schedules. It allows users to set up scaling actions to occur at specific times or intervals, optimizing resource allocation

==== Purpose
The purpose of scheduled scaling is to scale the number of pods based on predefined schedule. In the above code, if enabled, the pod will scale up from Monday to Friday at 1 AM and at 10 AM respectively with number of replicas(5) mentioned, and again on the in the same time period the pod will scale down at 2 AM and 12 PM respectively to the number of replicas(1) mentioned. 

=== Pod Disruption Budget

==== Code

[source, yaml]
pdb:
  minAvailable:
  annotations: {}

==== About
A Pod Disruption Budget is a Kubernetes resource that specifies the minimum number of pods that must remain available during a disruption caused.


==== Purpose
If mentioned minAvailable, PDB in any condition maintains the minAvailable number of pods. In the above code, if minAvailable field is set to 1, indicating that at least one instances of the specified pods should be available at any given time.

=== Istio

==== Code

[source, yaml]

istio:
  enabled: false

==== About
Istio is a configurable, open source service-mesh layer that secures the containers in a Kubernetes cluster.

==== Purpose
If set as true, then istio gateway is enabled.

=== Custom Java Options
Requirement Type : OPTIONAL

==== Code

[source, yaml]
opts:
  custom_javaopts_configurations:
    - -Djavax.net.debug=handshake,failure
    - -Dsnapstore.config=/usr/local/tomcat/conf/snapstore.config
    - -Dcom.sun.management.jmxremote
    - -Dcom.sun.management.jmxremote.rmi.port=8888
    - -Dcom.sun.management.jmxremote.ssl=false
    - -Dcom.sun.management.jmxremote.authenticate=false
    - -javaagent:/jmx-entrypoint-init/jmx_prometheus_javaagent-0.12.0.jar=8871:/jmx-entrypoint-init/jmx-config.yml
    - -XX:+HeapDumpOnOutOfMemoryError
    - -XX:OnOutOfMemoryError='kill -9 %p'

==== About
Java options are configuration parameters passed to the Java Virtual Machine (JVM) when starting a Java application. These options control various aspects of the JVM's behavior, including memory management, debugging, and performance tuning.

==== Purpose
Custom Java options allow fine-tuning of the JVM for optimal performance and debugging capabilities. The options specified in the configuration include:

- SSL debugging for troubleshooting connection issues
- JMX remote monitoring configuration for performance monitoring
- Memory management settings to handle out-of-memory situations
- Prometheus JVM monitoring agent for metrics collection

=== Custom Catalina Options
Requirement Type : OPTIONAL

==== Code

[source, yaml]
opts:
  custom_catalinaopts_configurations:
    enable: false
    parameters: 
      - -Dsnapstore.rds_aws_secret_name=${RDS_USER_NAME}
      - -Dsnapstore.rds_aws_secret_password=${RDS_PASSWORD}
      - -Dsnapstore.truststore_password=${TRUSTSTORE_PASSWORD}
      - -Dsnapstore.certificate_key_password=${CERTIFICATE_KEY_PASSWORD}
      - -Dsnapstore.certificate_keystore_password=${CERTIFICATE_KEYSTORE_PASSWORD}

==== About
Catalina options are specific configuration parameters for Apache Tomcat, the servlet container used to run the eSync application. These options control Tomcat-specific behaviors and configurations.

==== Purpose
Custom Catalina options allow for configuring Tomcat with environment-specific parameters, particularly for handling sensitive information like credentials through environment variables. When enabled, these options provide a secure way to pass credentials to the application without hardcoding them.

=== Custom Config JSON
Requirement Type : OPTIONAL

==== Code

[source, yaml]
opts:
  custom_configjson_configurations:
    enable: true
    configs:
      showAdHocInstallationPolicies: false
      hideInstallationPolicies: false
      showVehicleStatus: false
      # logo:
      #   imageSource:
      #     absolute: https://esync-test.excelfore.com/static/assets/sotauiv4/customer_logo.png

==== About
Custom Config JSON provides configuration options for the eSync UI frontend. These settings control the visibility and behavior of various UI elements.

==== Purpose
The custom config JSON allows for customizing the user interface experience without requiring code changes. It controls which features are visible to users and can be used to brand the UI with custom logos and styling.

=== Fluentd Logger
Requirement Type : OPTIONAL

==== Code

[source, yaml]
fluentd:
  repository: excelfore/esync
  enabled: false
  pullPolicy: IfNotPresent
  tag: core-master-latest-logger-all
  annotations: {}
  port: 24224
  logger:
    stdout:
      enabled: true
    graylog:
      enabled: false
      host: ************
      port:
      flush_interval: 5s
      system:
        workers: 2
    file:
      enabled: false
      path: /usr/local/tomcat/logs
    elasticSearch:
      enabled: false
      elastic_search_end_point:
      region:
      flush_interval: 1s
      flush_thread_count: 10
      workers: 2
    azureblob:
      enabled: false
      azure_storage_account:
      azure_storage_access_key:
      azure_storage_connection_string:
      azure_storage_sas_token:
      azure_container: eSyncLogs
      path: /var/log/fluent/azurestorageappendblob
      timekey: 10
      timekey_wait: 5
    s3:
      enabled: false
      aws_key_id:
      aws_sec_key:
      s3_bucket:
      s3_region:
      path: /usr/local/tomcat/s3/logs

==== About
Fluentd is an open-source data collector for unified logging. It allows for collecting, processing, and forwarding logs to various destinations.

==== Purpose
The Fluentd logger configuration enables comprehensive logging for the eSync application. It supports multiple output destinations:

- Standard output for local development and debugging
- Graylog for centralized log management
- File-based logging for persistent storage
- Elasticsearch for searchable log analytics
- Azure Blob Storage for cloud-based log archiving
- Amazon S3 for scalable log storage

When enabled, Fluentd collects application logs and forwards them to the configured destinations, facilitating troubleshooting and monitoring.

=== Termination Grace Period
Requirement Type : OPTIONAL

==== Code

[source, yaml]
terminationGracePeriod:
  enabled: true
  seconds: 60

==== About
The termination grace period is the time given to a pod to gracefully shut down before it is forcibly terminated.

==== Purpose
Setting a termination grace period ensures that the application has sufficient time to complete in-flight requests, close connections, and perform cleanup operations before being terminated. This helps prevent data loss and ensures a clean shutdown process.

=== OpenTelemetry Integration
Requirement Type : OPTIONAL

==== Code

[source, yaml]
signoz_status: false
otel_status: false
otelHandler:
  repository: container-registry-internal.excelfore.com/excelfore/cap
  pullPolicy: IfNotPresent
  tag: fetch-otel

custom_otelopts_configurations:
  - -javaagent:/otel-entrypoint-init/opentelemetry-javaagent.jar
  - -XX:+HeapDumpOnOutOfMemoryError
  - -XX:OnOutOfMemoryError='kill -9 %p'
  - -Dotel.exporter.otlp.endpoint=http://signoz.excelfore.com:4318
  - -Dotel.resource.attributes=service.name=esync
  - -Dotel.metrics.exporter=otlp
  - -Dotel.traces.exporter=otlp
  - -Dotel.logs.exporter=otlp
  - -Dotel.javaagent.debug=true

==== About
OpenTelemetry is an observability framework for cloud-native software that provides tools for collecting, processing, and exporting telemetry data (metrics, logs, and traces).

==== Purpose
The OpenTelemetry integration enables comprehensive monitoring and observability for the eSync application. When enabled, it collects:

- Distributed traces to track request flows across services
- Metrics for performance monitoring
- Logs for troubleshooting

The configuration includes options for connecting to SignOZ (an open-source observability platform) and customizing the OpenTelemetry agent behavior.
