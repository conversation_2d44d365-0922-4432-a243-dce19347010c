=== Horizontal Pod AutoScaler

==== Code

[source, yaml]
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  name: esync-esync-server-chart
  labels:
    helm.sh/chart: esync-server-chart-2.0.0
    app.kubernetes.io/name: esync-server-chart
    app.kubernetes.io/instance: esync
    app: esync-server-chart
    app.kubernetes.io/version: "2.0.0"
    app.kubernetes.io/managed-by: Helm
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: esync-esync-server-chart
  minReplicas: 1
  maxReplicas: 100
  metrics:
    - type: Resource
      resource:
        name: cpu
        targetAverageUtilization: 30

==== About
Horizontal Pod Autoscaler is a component that automatically scales workloads to additional nodes in response to increased load.

==== Purpose
HPA continuously monitors the server for resource usage. Based on the collected resource usage, HPA will calculate the desired number of replicas required. Then, HPA decides to scale up the application to the desired number of replicas. In the above code,

scaleTargetRef: It is a named reference to which the resource is being scaled. 

minReplicas: The lower limit for the number of replicas to which the autoscaler can scale down. 

maxReplicas: The upper limit which the autoscaler can go. 

The metrics is used to calculate the desired replica count. In the above case  it is using the Resource type, which tells the HPA to scale the deployment based on average CPU utilization.

averageUtilization: It is set to a threshold value of 30. If it crosses the averageUtilization then it increases the number of replicas else it decreases the number of replicas to the limit defined.