=== Deployment

==== Code

[source, yaml]
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
        checkov.io/skip1: CKV_K8S_40=Containers should run as a high UID to avoid host conflict
        checkov.io/skip2: CKV_K8S_43=Image should use digest
        checkov.io/skip3: CKV_K8S_38=Ensure that Service Account Tokens are only mounted where
          necessary
        seccomp.security.alpha.kubernetes.io/pod: runtime/default
  namespace: esync 
  name: esync-esync-server-chart
  labels:
    helm.sh/chart: esync-server-chart-2.0.0
    app.kubernetes.io/name: esync-server-chart
    app.kubernetes.io/instance: esync
    app: esync-server-chart
    app.kubernetes.io/version: "2.0.0"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app.kubernetes.io/name: esync-server-chart
      app.kubernetes.io/instance: esync
      app: esync-server-chart
  template:
    metadata:
      annotations:
        checkov.io/skip1: CKV_K8S_40=Containers should run as a high UID to avoid host conflict
        checkov.io/skip2: CKV_K8S_43=Image should use digest
        checkov.io/skip3: CKV_K8S_38=Ensure that Service Account Tokens are only mounted where
          necessary
        seccomp.security.alpha.kubernetes.io/pod: runtime/default
      labels:
        app.kubernetes.io/name: esync-server-chart
        app.kubernetes.io/instance: esync
        app: esync-server-chart
    spec:
      imagePullSecrets:
        - name: esync-core    
      serviceAccountName: esync-service-account
      securityContext:
        {}
      initContainers:
        - name: esync-db-migration
          image: "excelfore/esync:core-master-latest-migration-all"
          imagePullPolicy: IfNotPresent
          command: ["/bin/sh","-c"]
          args:
          - /xl4migration/entrypoint.sh
          resources:
            {}
          securityContext:
            {}
          env:
            - name: "xl4.appshack.cmd.fluentd.port"
              value: "24224"              
          volumeMounts:
            - name: secret-mount-reload-esync-certificate
              mountPath: /opt/soft/appshack/pki
              readOnly: true
            - name: fluentd-config-map
              mountPath: /fluentd/etc/fluentxl4.conf
              subPath: fluentxl4.conf
            - name: configmap-snapstore-config
              mountPath: /snapstore.config
              subPath: snapstore.config
        - name: fetch
          image: "excelfore/esync:fluentd-jdk-handle-0.5"
          imagePullPolicy: IfNotPresent
          resources:
              requests:
                  cpu: 100m
                  memory: 0.2Gi
              limits:
                  cpu: 100m
                  memory: 0.2Gi
          command: ["/bin/sh","-c"]
          securityContext:
            {}           
          args: ["mkdir -p /fluentd-entrypoint-init; cp /fluentd/fluentd-jdk-handler.jar /fluentd-entrypoint-init/"]
          volumeMounts:
            - mountPath: /fluentd-entrypoint-init
              name: dump
        - name: fetch-jar
          imagePullPolicy: IfNotPresent
          image: "excelfore/esync:jmx-configuration-0.1"
          command: ["/bin/sh","-c"]
          resources:
              requests:
                  cpu: 100m
                  memory: 0.2Gi
              limits:
                  cpu: 100m
                  memory: 0.2Gi
          securityContext:
            {}           
          args: ["mkdir -p /jmx-entrypoint-init; cp /jmx/jmx_prometheus_javaagent-0.12.0.jar /jmx-entrypoint-init/; cp /jmx/jmx-config.yml /jmx-entrypoint-init/"]
          volumeMounts:
            - mountPath: /jmx-entrypoint-init
              name: jmxdump
      containers:
        - name: esync-redis
          image: "redis/redis-stack-server:latest"
          imagePullPolicy: Always
          resources:
            {}
          securityContext:
            {}
          volumeMounts:
            - mountPath: /data
              name: redis-data
        - image: "excelfore/esync:core-master-latest-logger-all"
          imagePullPolicy: IfNotPresent
          command: ["/bin/sh","-c"]
          args:
          - fluentd -c /fluentd/etc/fluentxl4.conf -p /fluentd/plugins --gemfile /fluentd/Gemfile;      
          name: fluentd
          securityContext:
            {}
          ports:
          - containerPort: 24224
          resources: {}
          volumeMounts:       
          - name: fluentd-config-map
            mountPath: /fluentd/etc/fluentxl4.conf
            subPath: fluentxl4.conf 
          - mountPath: "/usr/local/tomcat/logs"
            name: logs-pvc
        - name: esync-server-chart
          securityContext:
            {}
          image: "excelfore/esync:core-master-latest"
          imagePullPolicy: IfNotPresent
          env:                               
            - name: MY_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: MY_POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: MY_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: MY_POD_SERVICE_ACCOUNT
              valueFrom:
                fieldRef:
                  fieldPath: spec.serviceAccountName           
          ports:
          - containerPort: 8443
          - containerPort: 9080
          - containerPort: 9086
          - containerPort: 9084
          resources: {}
          readinessProbe:
            httpGet:
              path: /snap/health?source=uO5wW3yR7nN2jG4v
              port: 9080
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 300
            successThreshold: 1
            failureThreshold: 6
          livenessProbe:
            httpGet:
              path: /snap/health?source=uO5wW3yR7nN2jG4v
              port: 9080              
            initialDelaySeconds: 300
            periodSeconds: 180
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 6
          volumeMounts:
          - name: configmap-snapstore-config
            mountPath: /usr/local/tomcat/conf/snapstore.config
            subPath: snapstore.config
          - name: configmap-snapstore-workflow-xml
            mountPath: /usr/local/tomcat/conf/snapstore_workflow.xml
            subPath: snapstore_workflow.xml
          - name: configmap-appshack-logging-properties
            mountPath: /usr/local/tomcat/conf/logging.properties
            subPath: logging.properties      
          - name: configmap-demo-device-groups-config
            mountPath: /usr/local/tomcat/conf/demo_device_groups.xml
            subPath: demo_device_groups.xml
          - name: configmap-quartz-xl4-properties
            mountPath: /usr/local/tomcat/conf/quartz_xl4.properties
            subPath: quartz_xl4.properties    
          - name: configmap-server-xl4
            mountPath: /usr/local/tomcat/conf/server.xml
            subPath: server.xml
          - name: appshack-static-config-mount
            mountPath: /usr/local/tomcat/conf/static/assets/sotauiv4/config.json 
            subPath: config.json
          - name: configmap-setenv-sh
            mountPath: /usr/local/tomcat/bin/setenv.sh
            subPath: setenv.sh 
          - mountPath: /fluentd-entrypoint-init
            name: dump
          - mountPath: /jmx-entrypoint-init
            name: jmxdump
          - name: configmap-acl-device-xml
            mountPath: /usr/local/tomcat/conf/acl-device.xml
            subPath: acl-device.xml
          - name: secret-mount-reload-esync-certificate
            mountPath: /opt/soft/appshack/pki
            readOnly: true
          - mountPath: "/usr/local/tomcat/logs"
            name: logs-pvc
      volumes:
      - name: fluentd-config-map
        configMap:
          name: fluentd-config-map
      - emptyDir: {}
        name: dump
      - emptyDir: {}
        name: redis-data
      - emptyDir: {}
        name: jmxdump
      - name: secret-mount-reload-esync-certificate
        secret:
          secretName: esync-certificate
      - name: logs-pvc
        emptyDir: {}
      - name: configmap-snapstore-config
        configMap:
          name: configmap-snapstore-config
      - name: configmap-snapstore-workflow-xml
        configMap:
          name: configmap-snapstore-workflow-xml
      - name: configmap-appshack-logging-properties
        configMap:
          name: configmap-appshack-logging-properties
      - name: configmap-demo-device-groups-config
        configMap:
          name: configmap-demo-device-groups-config
      - name: configmap-quartz-xl4-properties
        configMap:
          name: configmap-quartz-xl4-properties
      - name: configmap-server-xl4
        configMap:
          name: configmap-server-xl4
      - name: appshack-static-config-mount
        configMap:
          name: appshack-static-config-mount
      - name: configmap-setenv-sh
        configMap:
          name: configmap-setenv-sh
      - name: configmap-acl-device-xml
        configMap:
          name: configmap-acl-device-xml


==== About
A Deployment manages a set of Pods to run an application workload, usually one that doesn't maintain state.

==== Purpose
Using Deployment one can ensure that number of pods mentioned in the above code (replica) is always maintained. Even if one tries to delete it, the number of pods mentioned is always present by recreating it.

Initially the init containers are executed, after the completion of init container the main containers are executed. The secret created is attached in image pull secrets section for the containers to pull the images from private repositories.

Through the labels and selectors service identifies the respective pods and directs the traffic into it. For the eSync Server the respective config maps and secrets which are needed are attached. 