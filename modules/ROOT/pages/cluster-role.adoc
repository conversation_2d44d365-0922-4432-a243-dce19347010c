=== Cluster Role

==== Code

[source, yaml]
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: esync-esync-server-chart   
rules:
- apiGroups: ["", "apps"]
  resources: ["*"]
  verbs: ["patch", "get", "watch", "list"]

==== About
Role Based Access Control (RBAC) refers to assigning permissions to users based on their role. In that Cluster role defines a set of permissions across the cluster. It allows you to specify what actions (verbs) a user or group can perform on which resources (nouns) throughout the entire cluster.

==== Purpose
The purpose of cluster role is to define the rules for the user so that only certain user can access certain resource which helps in security aspect. 

In the above code ClusterRole named esync-esync-server-chart grants permissions to perform patch, get, watch and list actions on all resources across the entire Kubernetes cluster.