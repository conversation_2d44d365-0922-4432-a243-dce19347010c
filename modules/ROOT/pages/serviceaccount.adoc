=== Service Account

=== Code

[source, yaml]
apiVersion: v1
kind: ServiceAccount
metadata:
  name: esync-service-account
  annotations:
  namespace: esync
  labels:
    helm.sh/chart: esync-server-chart-2.0.0
    app.kubernetes.io/name: esync-server-chart
    app.kubernetes.io/instance: esync
    app: esync-server-chart
    app.kubernetes.io/version: "2.0.0"
    app.kubernetes.io/managed-by: Helm

=== About
A service account is a account that, provides a distinct identity in a cluster. Application Pods, system components, and entities inside and outside the cluster can use a specific ServiceAccount's credentials to identify as that ServiceAccount.

=== Purpose
Service accounts also provides an identity for pods to authenticate with the Kubernetes API. Each pod can be associated with a specific service account, which determines its permissions and access level. Service account helps in RBAC specifying what each pods can perform the respective actions.