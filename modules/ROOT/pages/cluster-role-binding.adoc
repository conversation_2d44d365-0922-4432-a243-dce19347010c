=== Cluster Role Binding

==== Code

[source, yaml]
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: esync-esync-server-chart   
subjects:
- kind: ServiceAccount
  name: esync-service-account
  namespace: esync        
roleRef:
  kind: ClusterRole
  name: esync-esync-server-chart
  apiGroup: rbac.authorization.k8s.io

==== About
A ClusterRoleBinding is resource that binds a ClusterRole to a user, group, or service account. It grants the permissions defined in the ClusterRole to the subjects specified in the binding. ClusterRoleBindings apply cluster-wide and are useful for assigning  permissions to users or groups across the entire cluster.

==== Purpose
For the created cluster role, ClusterRoleBinding named esync-esync-server-chart binds the ClusterRole to the service account named esync-service-account. It grants esync-service-account the permissions defined in the ClusterRole across the entire cluster.