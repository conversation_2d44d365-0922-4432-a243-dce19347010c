=== Virtual Service

==== Code

[source, yaml]

apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: esync-esync-server-chart-virtual-service-ui
spec:
  hosts:
  - esync-test.excelfore.com
  gateways:
  - esync-esync-server-chart-ui
  tcp:
  - match:
    - port: 9080
    route:
    - destination:
        host: "esync-esync-server-chart.esync.svc.cluster.local"
        port:
          number: 9080

[source, yaml]
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: esync-esync-server-chart-virtual-service-device
spec:
  hosts:
  - esync-test.excelfore.com
  gateways:
  - esync-esync-server-chart-device
  tcp:
  - match:
    - port: 8443
    route:
    - destination:
        host: "esync-esync-server-chart.esync.svc.cluster.local"
        port:
          number: 8443

[source, yaml]

apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: esync-esync-server-chart-virtual-service-tsp
spec:
  hosts:
  - esync-test.excelfore.com
  gateways:
  - esync-esync-server-chart-tsp
  tcp:
  - match:
    - port: 9084
    route:
    - destination:
        host: "esync-esync-server-chart.esync.svc.cluster.local"
        port:
          number: 9084


==== About
VirtualService is a  component of the Istio service mesh that allows us to configure how requests are routed to various services within the mesh. It adds additional capabilities than the normal service file.


==== Purpose
The purpose of virtual service is it lets you add additional capabilities from the normal service. You can split the traffic to the various destination based on the weightage. 

In the above code virtual service receives the traffic from the gateway mentioned once it receives the traffic it is directing the whole traffic to host (service) specified on respective port number.