=== Network Policy

==== Code

[source, yaml]
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: release-name-esync-server-chart
  namespace: esync 
spec:
  podSelector:
    matchLabels:
      app: esync-server-chart
  policyTypes:
  - Ingress      
  ingress:
  - from:
    - ipBlock:
        cidr: 0.0.0.0/0
        except: [**********/24]
    - namespaceSelector:      
        matchLabels:
          app: esync-server-chart
    - podSelector:      
        matchLabels:
          app: esync-server-chart

==== About
NetworkPolicy is a Kubernetes object that enables the creation of policies to restrict the communication between pods and external entities in a namespace, using various factors like IP addresses, ports, protocols, and labels. The ingress section defines incoming traffic rules while the egress section defines outgoing traffic rules. NetworkPolicy uses podSelector to select pods based on their labels, namespaceSelector to select pods in particular namespaces, and ipBlock to specify IP address blocks allowed or denied access to pods.

==== Purpose
By default Kubernetes allow unrestricted communication between the pods and external access. 

In the above code, The podSelector field selects pods based on their labels and determines which pods the policy applies to. In this case, the NetworkPolicy targets pods labeled with name esync-server-chart. 

The ingress section defines to allow all the incoming traffic except the ip block defined and allow traffic from all the pods from namespace esync-server-chart and from the pod esync-server-chart.