=== Secret

==== Code

[source, yaml]
apiVersion: v1
kind: Secret
metadata:
  namespace:
  name: esync-core
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ********

==== About
Kubernetes Secrets are ones which store sensitive information such as passwords, tokens, and certificates in a secure way.

==== Purpose
Docker config json are used to store docker credentials. In the above code, they are used by the Kubernetes container runtime to pull images from private Docker registries.