# Default values for esync-server-chart.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
replicaCount: 1

dbMigration:
  enabled: true
  image:
    repository: excelfore/esync
    pullPolicy: IfNotPresent
    tag: esync-release-6.2.1-RC.6-migration-base
  resources: {}
   # requests:
   #   cpu: 500m
   #   memory: 500Mi
   # limits:
   #   cpu: 500m
   #   memory: 1Gi

image:
  repository: excelfore/esync
  pullPolicy: IfNotPresent
  tag: esync-release-6.2.1-RC.6
cron:
  enabled: true
  cron_schedule: "*/60 * * * *"
  cron_image: bitnami/postgresql
  concurrencyPolicy: Forbid

rds:
  rds_db_port: 5432
  rds_db_name: snapstore
  rds_db_user_name: snapstore #${snapstore.rds_aws_secret_name} 
  rds_db_password: snapstore #${snapstore.rds_aws_secret_password}
  pool_max_active: 10
  pool_min_idle: 10
  pool_max_idle: 10
  rds_db_url: esync-postgres-postgresql.esync.svc.cluster.local

redis:
  enabled: true #false
  runAsContainer: true
  resources: {}
  direct: true 
  endpoint_url: localhost #redis_endpoint_url_value #elastic-cache-g-d1468.brtwer.ng.0001.use1.cache.amazonaws.com
  port: 6379

domain: esync-test.excelfore.com

snapstore:
  login_type: demo,ssl
  snap_url_protocol: https
  demo_login_user: Excelfore User
  demo_login_password: 3dWx31w56
  demo_login_role: OTA ADMIN
  admin_password: 
  device_port_max_threads: 10
  UI_port_max_threads: 10
  certificates:
    signature_cert_chain_path: /opt/soft/appshack/pki/cert.pem
    signature_private_key_path: /opt/soft/appshack/pki/private.pem
    signature_private_key_password:  #${snapstore.truststore_password}    
    signature_trust_store_path: /opt/soft/appshack/pki/signature_truststore.jks
    signature_trust_store_password: 3dWx31w56 #${snapstore.truststore_password}    
    server_signature_trust_store_path: /opt/soft/appshack/pki/server_signature_truststore.jks
    server_signature_trust_store_password: 3dWx31w56 #${snapstore.truststore_password}

# NodePort/ClusterIP/LoadBalancer
service:
  internal: false
  awsLoadBalancerSslCert: 
  annotations: {}
  loadBalancerTimeOutInSeconds: 4000
  type: LoadBalancer  #LoadBalancer
  endpoints:
    oma:
      port: 8443
      targetPort: 8443
      protocol: TCP
      nodePort:
    snap:
      port: 9080
      targetPort: 9080
      protocol: TCP
      nodePort:
    tsp:
      port: 9084
      targetPort: 9084
      protocol: TCP
      nodePort:
    sslsso:
      port: 9086
      targetPort: 9086
      protocol: TCP
      nodePort:

serverxml:
  oma:
    enable: true
    ssl: true
    port_max_threads: 150
    truststore_jks_path: /opt/soft/appshack/pki/device_connector_truststore.jks
    truststore_jks_password: sM8AexdC66Q8 #${snapstore.truststore_password}
    truststore_certificate_verification: optional
    protocols: TLSv1.2
    keystore_jks_path: /opt/soft/appshack/pki/device_connector_keystore.jks
    certificate_keystore_password: rTHvfB667hS6 #${snapstore.certificate_keystore_password}
    certificate_key_alias: xl4cert
    certificate_key_password: Y4r3A65bMdVw #${snapstore.certificate_key_password}
    keystore_certificate_verification: want
  snap:
    enable: true
    ssl: false
    port_max_threads: 150
    keystore_jks_path: /opt/soft/appshack/pki/frontend.jks
    certificate_keystore_password: 51Hm79Q1CzrX
    certificate_key_alias: xl4cert
    certificate_key_password: qkW083vl2Bkm
    keystore_certificate_verification: optional
  tsp:
    enable: true
    ssl: true
    port_max_threads: 150
    truststore_jks_path: /opt/soft/appshack/pki/tsp_truststore.jks
    truststore_jks_password: 52oB9TnJZ4Wn #${snapstore.truststore_password}
    truststore_certificate_verification: optional
    protocols: TLSv1.2
    keystore_jks_path: /opt/soft/appshack/pki/tsp_keystore.jks
    certificate_keystore_password: 48RU3ZzcVpam #${snapstore.certificate_keystore_password}
    certificate_key_alias: xl4cert
    certificate_key_password: zsO9AC46xx03 #${snapstore.certificate_key_password}
    keystore_certificate_verification: optional
  sslsso:
    enable: true
    ssl: true
    port_max_threads: 150
    truststore_jks_path: /opt/soft/appshack/pki/sso_truststore.jks
    truststore_jks_password: Jj4yJiJ45Kj7 #${snapstore.truststore_password}
    truststore_certificate_verification: optional
    protocols: TLSv1.2
    keystore_jks_path: /opt/soft/appshack/pki/sso_keystore.jks
    certificate_keystore_password: U5Xbit4fVq6H #${snapstore.certificate_keystore_password}
    certificate_key_alias: xl4cert
    certificate_key_password: XkvOuga11w20 #${snapstore.certificate_key_password}
    keystore_certificate_verification: optional

opts:
  snapstore_workflow:
    campaign_approver_enable: false
    package_approver_enable: false

  custom_javaopts_configurations:
    - -Djavax.net.debug=handshake,failure
    - -Dsnapstore.config=/usr/local/tomcat/conf/snapstore.config
    - -Dcom.sun.management.jmxremote
    - -Dcom.sun.management.jmxremote.rmi.port=8888
    - -Dcom.sun.management.jmxremote.ssl=false
    - -Dcom.sun.management.jmxremote.authenticate=false
    - -javaagent:/jmx-entrypoint-init/jmx_prometheus_javaagent-0.12.0.jar=8871:/jmx-entrypoint-init/jmx-config.yml
    - -XX:+HeapDumpOnOutOfMemoryError
    - -XX:OnOutOfMemoryError='kill -9 %p'

  custom_catalinaopts_configurations:
    enable: false #true
    parameters: 
      - -Dsnapstore.rds_aws_secret_name=${RDS_USER_NAME}
      - -Dsnapstore.rds_aws_secret_password=${RDS_PASSWORD}
      - -Dsnapstore.amq_aws_secret_name=${AMQ_USER_NAME}
      - -Dsnapstore.amq_aws_secret_password=${AMQ_PASSWORD}
      - -Dsnapstore.truststore_password=${TRUSTSTORE_PASSWORD}
      - -Dsnapstore.certificate_key_password=${CERTIFICATE_KEY_PASSWORD}
      - -Dsnapstore.certificate_keystore_password=${CERTIFICATE_KEYSTORE_PASSWORD}

  custom_configjson_configurations:
      enable: true
      configs:
        showAdHocInstallationPolicies: false
        hideInstallationPolicies: false
        showVehicleStatus: false
        # logo:
        #   imageSource:
        #     absolute: https://esync-test.excelfore.com/static/assets/sotauiv4/customer_logo.png

  custom_snapstore_configurations:
    - key: snapstore.signer_auth.skip
      value: true
    - key: snapstore.signature_check.skip
      value: true  
    - key: snapstore.dgc.disabled
      value: true
    - key: snapstore.jms.clear_prepared
      value: true
    - key: snapstore.demo_db.use_schema
      value: true
    - key: snapstore.cdb.single_schema
      value: true
    - key: snapstore.sso.config.demo
      value: demo
    - key: snapstore.demo_sso.demo.excelfore.default.roles
      value: OTA ADMIN
    - key: snapstore.sso.config.ssl
      value: ssl
    - key: snapstore.backend_license_certificate
      value: '/opt/soft/appshack/pki/backend_license_certificate.pem'
    - key: snapstore.backend_license_trust
      value: '/opt/soft/appshack/pki/backend_license_trust.pem'
    # Enable if you what to enable the remote delta and URL signing      
    # - key: snapstore.url-sign.s3.buckets
    #   value: url-sign-bucket-1,url-sign-bucket-2
    # - key: snapstore.url_sign.expiration_min
    #   value: 15
    # - key: snapstore.delta.difference
    #   value: "core.remote-es-diff"
    # - key: snapstore.rsd.url
    #   value: "https://delta-server.com"
    # - key: snapstore.rsd.bucket
    #   value: "delta_bucket"
    # - key: snapstore.rsd.poll_interval_ms
    #   value: "120000"
    # - key: snapstore.rsd.timeouts_ms
    #   value: "60000"
    # - key: snapstore.rsd.cert_chain
    #   value: "/opt/soft/appshack/pki/delta_server_certificate.pem"
    # - key: snapstore.rsd.private_key
    #   value: "/opt/soft/appshack/pki/delta_server_private.pem"

fluentd:
  repository: excelfore/esync
  enabled: false
  pullPolicy: IfNotPresent
  tag: esync-release-6.2.1-RC.6-logger-base
  annotations: {}
  port: 24224
  logger:
    stdout:
      enabled: true
    graylog:
      enabled: false
      host: ************
      port:
      flush_interval: 5s
      system:
        workers: 2
    file:
      enabled: false
      path: /usr/local/tomcat/logs
    elasticSearch:
      enabled: false
      elastic_search_end_point:
      region:
      flush_interval: 1s
      flush_thread_count: 10
      workers: 2
    azureblob:
      enabled: false
      azure_storage_account:
      azure_storage_access_key:
      azure_storage_connection_string:
      azure_storage_sas_token:
      azure_container: eSyncLogs
      path: /var/log/fluent/azurestorageappendblob
      timekey: 10
      timekey_wait: 5
    s3:
      enabled: false
      aws_key_id:
      aws_sec_key:
      s3_bucket:
      s3_region:
      path: /usr/local/tomcat/s3/logs
      timekey: 300
      timekey_wait: 1m
      chunk_limit_size: 10m
    opensearch:
      enabled: false
      open_search_end_point:
      access_key_id:
      secret_access_key:
      region: us-east-2
      flush_interval: 5s
      refresh_credentials_interval: 1h
      logstash_prefix: esync
      buffer:
        buffer_type: memory
        num_threads: 2
        flush_thread_count: 8
        flush_interval: 1s
        chunk_limit_size: 10M
        queue_limit_length: 16
        retry_max_interval: 30
        retry_forever: true
      system:
        workers: 2

fluentdHandler:
  repository: excelfore/esync
  pullPolicy: IfNotPresent
  tag: fluentd-jdk-handle-0.6

jmxConfiguration:
  repository: excelfore/esync
  pullPolicy: IfNotPresent
  tag: jmx-configuration-0.1

nameOverride: ""
fullnameOverride: ""
namespace: esync
serviceAccount:
  create: true #false
  annotations: {}
  name: "esync-service-account" #"aws-secret-sa-g-d1468"

podAnnotations: 
  seccomp.security.alpha.kubernetes.io/pod: "runtime/default"       
  checkov.io/skip1: CKV_K8S_40=Containers should run as a high UID to avoid host conflict
  checkov.io/skip2: CKV_K8S_43=Image should use digest
  checkov.io/skip3: CKV_K8S_38=Ensure that Service Account Tokens are only mounted where necessary

podSecurityContext: {}
  # fsGroup: 501

securityContext: {}
  # privileged: false
  # allowPrivilegeEscalation: false
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: false
  # runAsNonRoot: true
  # runAsUser: 501

livenessProbe:
  enabled: true
  initialDelaySeconds: 300
  periodSeconds: 180
  timeoutSeconds: 5
  failureThreshold: 6
  successThreshold: 1

readinessProbe:
  enabled: true
  initialDelaySeconds: 5
  periodSeconds: 10
  timeoutSeconds: 300
  failureThreshold: 6
  successThreshold: 1

startupProbe:
  enabled: false
  exec:
    command: ["ls"]
  initialDelaySeconds: 0
  periodSeconds: 40
  timeoutSeconds: 1
  failureThreshold: 6
  successThreshold: 1

ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: esync-test.excelfore.com
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []

resources:
  enabled: true
  fluentd:
    cpu: 500m
    memory: 500Mi
  eSync:
    cpu: 1000m
    memory: 2Gi

autoscaling:
  enabled: false
  minReplicas: 1
  annotations: {}
  maxReplicas: 100
  targetCPUUtilizationPercentage: 30
  # targetMemoryUtilizationPercentage: 80

strategy:
  type: Recreate  # Recreate / RollingUpdate
  rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1

nodeSelector: {}

tolerations: []

affinity: {}

annotations:
  
persistentVolume:
  storage: 2Gi
  reclaimPolicy: Retain

persistentVolumeClaim:
  requrestStorage: 2Gi
  storage: 2Gi

imageCredentials:
  registry: docker.io
  username: 
  password: 
  annotations: {}

imagePullSecrets:
  - name: esync-core
imageSecretName: esync-core

configmapReload:
  name: configmap-reload
  extraConfigmapMounts: []

secretMountReload:
  name: secret-mount-reload
  extraSecretMounts:
    - name: esync-certificate
      mountPath: /opt/soft/appshack/pki
      secretName: esync-certificate
      readOnly: true

secretKeys: []

networkPolicy:
  enabled: false
  annotations: {}
  ipBlock:
    cidr: "0.0.0.0/0"
    except_cidr: ["**********/24"]
  namespaceSelector:
    - name: app
      value: esync-server-chart
  podSelector:
    - name: app
      value: esync-server-chart

scheduledScaling:
  annotations: 
    seccomp.security.alpha.kubernetes.io/pod: "runtime/default"
  enabled: false
  scaleUp:
    - time: "0 1 * * 1-5"
      replica: 5
      name: early-morning-start
    - time: "0 10 * * 1-5"
      replica: 5
      name: mid-morning-start
  scaleDown:
    - time: "0 2 * * 1-5"
      replica: 1
      name: early-morning-end
    - time: "0 12 * * 1-5"
      replica: 1    
      name: mid-morning-end

pdb:
  minAvailable:
  annotations: {} 

rbac:
  annotations: {}

istio:
  enabled: false

terminationGracePeriod:
  enabled: true
  seconds: 60

signoz_status: false
otel_status: false
otelHandler:
    repository: container-registry-internal.excelfore.com/excelfore/cap
    pullPolicy: IfNotPresent
    tag: fetch-otel

custom_otelopts_configurations:
  - -javaagent:/otel-entrypoint-init/opentelemetry-javaagent.jar
  - -XX:+HeapDumpOnOutOfMemoryError
  - -XX:OnOutOfMemoryError='kill -9 %p'
  - -Dotel.exporter.otlp.endpoint=http://signoz.excelfore.com:4318
  - -Dotel.resource.attributes=service.name=esync
  - -Dotel.metrics.exporter=otlp
  - -Dotel.traces.exporter=otlp
  - -Dotel.logs.exporter=otlp
  - -Dotel.javaagent.debug=true