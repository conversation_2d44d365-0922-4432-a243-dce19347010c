# Namespace creation
```console
cd configurations && ./configure.sh <namespace> <path to folder containing pki(secrets file) folder> && cd ..
```
# Helm Chart

## Postgres Installation as helm
```console
helm install esync-postgres bitnami/postgresql --set global.postgresql.auth.database=snapstore --set global.postgresql.auth.username=snapstore --set global.postgresql.auth.password=snapstore --namespace <namespace>
```

## Dry run for the validation

To validate chart before installing the chart `:

```console
helm install <chart-name> -n  <namespace> ./ -f <path-to-values.yaml>/values.yaml --dry-run
```

## Installation of helm

To install the chart with namespace`:

```console
helm install <chart-name> -n  <namespace> ./ -f <path-to-values.yaml>/values.yaml
```
## Upgrade of helm

To install the chart with namespace`:

```console
helm upgrade --install <chart-name> -n  <namespace> ./ -f <path-to-values.yaml>/values.yaml
```

## Uninstalling the Chart

To uninstall/delete theesync-jms deployment:

```console
helm uninstall esync-server --namespace <namespace>
```

## Configuration

| Parameter                                  | Description                                   | Default                                                    |
|--------------------------------------------|-----------------------------------------------|------------------------------------------------------------|
| `replicaCount`                             | Number of pods                                | `1`                                                        |
| `image.repository`                         | Image repository                              | `docker.io/excelfore/esync`                                |
| `image.pullPolicy`                         | Image pull policy                             | `Always`                                                   |
| `image.tag `                               | Image tag                                     | `esync-release-4.2225-RC1`                                 |
| `cron.enabled`                             | Enables cron                                  | `true`                                                     |
| `cron.cron_schedule`                       | Cron time schedule                            | `*/60 * * * *`                                             |
| `cron.cron_image`                          | Cron image name                               | `bitnami/postgresql`                                       |
| `rds.rds_db_port`                          | DB port of rds                                | `5432`                                                     |
| `rds.rds_db_name`                          | DB name of rds                                | `snapstore`                                                |
| `rds.rds_db_user_name`                     | DB user name of rds                           | `snapstore`                                                |
| `rds.rds_db_password`                      | DB password of rds                            | `snapstore`                                                |
| `rds.pool_max_active`                      | Active DB access limit of users               | `10`                                                       |
| `rds.pool_min_idle`                        | Minimum DB access limit of users              | `10`                                                       |
| `rds.pool_max_idle`                        | Maximum DB access limit of users              | `10`                                                       |
| `rds.rds_db_url`                           | DB url of rds                                 | `esync-postgres-postgresql.esync.svc.cluster.local`        |
| `redis.enabled`                            | Enables redis                                 | `false`                                                    |
| `redis.direct`                             | Enables direct                                | `true`                                                     |
| `redis.endpoint_url`                       | Redis enpoint url                             | `<redis_endpoint_url_value>`                               |
| `redis.port`                               | Redis endpoint url port                       | `6379`                                                     |
| `secret.aws_secret_manager`                | Enables secret manager                        | `false`                                                    |
| `snapstore.login_type`                     | UI login type                                 | `demo,ssl`                                                 |
| `snapstore.demo_login_user`                | DEMO login username                           | `Excelfore User`                                           |
| `snapstore.demo_login_password`            | DEMO login password                           | `$2a$12$2EbifUqHii0zmUDxHHyuM.7xnZd.XhpfXy68Zy3PQK6p5Y7h0S11q`|
| `snapstore.demo_login_role`                | DEMO login role                               | `OTA ADMIN`                                                |
| `snapstore.server_dns_name`                | Server dns name                               | `windriver-test.esyncsdk.com`                              |
| `snapstore.admin_password`                 | Admin password                                | ``                                                         |
| `snapstore.truststore_jks_password`        | Truststore password of attached secret        | ``                                                         |
| `snapstore.certificate_keystore_password`  | Keystore password of attached secret          | ``                                                         |
| `snapstore.certificate_key_password`       | Certificate key password of attached secret   | ``                                                         |
| `snapstore.device_port_max_threads`        | Maximum thread limit for device port          | `1000`                                                     |
| `snapstore.UI_port_max_threads`            | Maximum thread limit for UI port              | `150`                                                      |
| `snapstore.common_port_max_threads`        | Maximum thread limit for common port          | `150`                                                      |
| `opts.custom_javaopts_configurations`      | Java configurations in list format            | `Djavax.net.debug=handshake,failure,Dsnapstore.config=/usr/local/tomcat/conf/snapstore.config,Dcom.sun.management.jmxremote,Dcom.sun.management.jmxremote.rmi.port=8888,Dcom.sun.management.jmxremote.ssl=false,Dcom.sun.management.jmxremote.authenticate=false,javaagent:/jmx-entrypoint-init/jmx_prometheus_javaagent-0.12.0.jar=8871:/jmx-entrypoint-init/jmx-config.yml,XX:+HeapDumpOnOutOfMemoryError,XX:OnOutOfMemoryError='kill -9 %p',Xms4096m -Xmx4096m`|
| `opts.custom_catalinaopts_configurations`  | Catalinaopts configurations in list format    | `Dsnapstore.rds_aws_secret_name=${RDS_USER_NAME},Dsnapstore.rds_aws_secret_password=${RDS_PASSWORD},Dsnapstore.amq_aws_secret_name=${AMQ_USER_NAME},Dsnapstore.amq_aws_secret_password=${AMQ_PASSWORD},Dsnapstore.truststore_password=${TRUSTSTORE_PASSWORD},Dsnapstore.certificate_key_password=${CERTIFICATE_KEY_PASSWORD},Dsnapstore.certificate_keystore_password=${CERTIFICATE_KEYSTORE_PASSWORD}`|
| `opts.custom_snapstore_configurations`     | Snapstore configurationin key and value list format| `key: snapstore.signer_auth.skip value: true, key: snapstore.signature_check.skip value: true`|
| `fluentd.repository`                       | Repository name to pull fluentd image from    | `docker.io/excelfore/esync`                                |
| `fluentd.pullPolicy`                       | Adds pull policy if not there                 | `Always`                                                   |
| `fluentd.tag`                              | Image tag name                                | `docker.io/excelfore/esync`                                |
| `fluentd.port`                             | fluentd port number                           | `24224`                                                    |
| `fluentd.logger.stdout.enabled`            | Enables fluentd logger output                 | `True`                                                     |
| `fluentd.logger.graylog.enabled`           | Enables graylog for fluentd logs              | `False`                                                    |
| `fluentd.logger.graylog.host`              | Graylog host                                  | `************`                                             |
| `fluentd.logger.graylog.port`              | Graylog port                                  | ``                                                         |
| `fluentd.logger.graylog.flush_interval`    | Flush interval time period of graylog         | `5s`                                                       |
| `fluentd.logger.file.enabled`              | Enables file storage for fluentd logs         | `True`                                                     |
| `fluentd.logger.file.path`                 | Path  to file for log storage                 | `/usr/local/tomcat/logs`                                   |
| `fluentd.logger.elasticSearch.enabled`     | Enables elasticSearch for fluentd logs        | `False`                                                    |
| `fluentd.logger.elasticSearch.elastic_search_end_point`| Endpoint url of elasticSearch            | ``                                                  |
| `fluentd.logger.elasticSearch.region`      | Cluster region of elasticSearch               | `us-west-1`                                                |
| `fluentd.logger.elasticSearch.flush_interval`| Flush interval time period of elasticSearch   | `1s`                                                     |
| `fluentd.logger.elasticSearch.flush_thread_count`| Flush thread count of elasticSearch           | `10`                                                 |
| `fluentd.logger.elasticSearch.workers`     | No. of worker node in elasticSearch           | `2`                                                        |
| `fluentd.logger.s3.enabled`                | Enables s3 for fluentd logs                   | `False`                                                    |
| `fluentd.logger.s3.aws_key_id`             | AWS acess key                                 | ``                                                         |
| `fluentd.logger.s3.aws_sec_key`            | AWS secret key                                | ``                                                         |
| `fluentd.logger.s3.s3_bucket`              | S3 bucket name                                | ``                                                         |
| `fluentd.logger.s3.s3_region`              | S3 bucket cluster region                      | ``                                                         |
| `fluentd.logger.s3.path`                   | Path to store s3 logs                         | `/usr/local/tomcat/s3/logs`                                |
| `fluentd.logger.s3.timekey`                | Timekey to write files                        | `300`                                                      |
| `fluentd.logger.s3.timekey_wait`           | Timekey interval                              | `1m`                                                       |
| `fluentd.logger.s3.chunk_limit_size`       | Size limit                                    | `10m`                                                      |
| `fluentdHandler.repository`                | Repo name to pull fluentd handler image from  | `docker.io/excelfore/esync`                                |
| `fluentdHandler.pullPolicy`                | Adds pull policy if not there                 | `IfNotPresent`                                             |
| `fluentdHandler.tag`                       | Image tag name                                | `fluentd-jdk-handle-0.3`                                   |
| `jmxConfiguration.repository`              | Repository name to pull jmx image from        | `docker.io/excelfore/esync`                                |
| `jmxConfiguration.pullPolicy`              | Adds pull policy if not there                 | `IfNotPresent`                                             |
| `jmxConfiguration.tag`                     | Image tag name                                | `jmx-configuration-0.1`                                    |
| `nameOverride`                             | Replaces the name of the chart in the Chart.yaml file, when this is used to construct Kubernetes object names| ``|
| `fullnameOverride`                         | Completely replaces the generated name        | ``                                                         |
| `namespace`                                | Namespace                                     | `esync`                                                    |
| `serviceAccount.create`                    | Service account create                        | `true`                                                     |
| `serviceAccount.annotations`               | Service account annotations                   | `{}`                                                       |
| `serviceAccount.name`                      | Service account name                          | `windriver-test-service-account`                           |
| `podAnnotations.seccomp.security.alpha.kubernetes.io/pod`|pod annotation for k8s security scan| `runtime/default`                                       |
| `podAnnotations.checkov.io/skip1`          | Resources scan for pod                        | `CKV_K8S_40=Containers should run as a high UID to avoid host conflict`|
| `podAnnotations.checkov.io/skip2`          | Resources scan for pod                        | `CKV_K8S_43=Image should use digest`                       |
| `podAnnotations.checkov.io/skip3`          | Resources scan for pod                        | `CKV_K8S_38=Ensure that Service Account Tokens are only mounted where necessary`|
| `podSecurityContext`                       | Pod security related context                  | ``                                                         |
| `securityContext`                          | Security related context                      | ``                                                         |
| `service.awsLoadBalancerSslCert`           | Add loadbalancer url                          | ``                                                         |
| `service.annotations`                      | Service annotations                           | ``                                                         |
| `service.loadBalancerTimeOutInSeconds`     | Loadbalancer timeout period                   | `4000`                                                     |
| `service.type`                             | Service type                                  | `LoadBalancer`                                             |
| `service.oma.name`                         | Service name                                  | `oma`                                                      |
| `service.oma.port`                         | Device port                                   | `8443`                                                     |
| `service.oma.targetPort`                   | Device port                                   | `8443`                                                     |
| `service.oma.protocol`                     | Protocol type                                 | `TCP`                                                      |
| `service.oma.nodePort`                     | Node port to connect cluster and service      | ``                                                         |
| `service.snap.name`                        | Service name                                  | `snap`                                                     |
| `service.snap.port`                        | UI port                                       | `9080`                                                     |
| `service.snap.targetPort`                  | UI port                                       | `9080`                                                     |
| `service.snap.protocol`                    | Protocol type                                 | `TCP`                                                      |
| `service.snap.nodePort`                    | Node port to connect cluster and service      | ``                                                         |
| `service.tsp.name`                         | Service name                                  | `tsp`                                                      |
| `service.tsp.port`                         | Common port                                   | `9084`                                                     |
| `service.tsp.targetPort`                   | Common port                                   | `9084`                                                     |
| `service.tsp.protocol`                     | Protocol type                                 | `TCP`                                                      |
| `service.tsp.nodePort`                     | Node port to connect cluster and service      | ``                                                         |
| `livenessProbe.enabled`                    | Enables liveness probe                        | `True`                                                     |
| `livenessProbe.initialDelaySeconds`        | Liveness probe init delay time                | `300`                                                      |
| `livenessProbe.periodSeconds`              | Liveness probe time period                    | `180`                                                      |
| `livenessProbe.timeoutSeconds`             | Liveness probe timeout period                 | `5`                                                        |
| `livenessProbe.failureThreshold`           | Liveness probe threshold of faliure           | `6`                                                        |
| `livenessProbe.successThreshold`           | Liveness probe threshold of success           | `1`                                                        |
| `readinessProbe.enabled`                   | Enables readiness probe                       | `True`                                                     |
| `readinessProbe.initialDelaySeconds`       | Readiness probe init delay time               | `5`                                                        |
| `readinessProbe.periodSeconds`             | Readiness probe time period                   | `10`                                                       |
| `readinessProbe.timeoutSeconds`            | Readiness probe timeout period                | `300`                                                      |
| `readinessProbe.failureThreshold`          | Readiness probe threshold of faliure          | `6`                                                        |
| `readinessProbe.successThreshold`          | Readiness probe threshold of success          | `1`                                                        |
| `ingress.enabled`                          | Enables Ingress                               | `false`                                                    |
| `ingress.className`                        | If classname alloted for ingress role         | ``                                                         |
| `ingress.annotations`                      | Ingress annotations (values are templated)    | `{}`                                                       |
| `ingress.hosts.host`                       | Ingress accepted hostnames                    | `windriver-test.esyncsdk.com`                              |
| `ingress.hosts.paths.path`                 | Ingress accepted path                         | `/`                                                        |
| `ingress.hosts.paths.pathType`             | Ingress path as implemented                   | `<ImplementationSpecific>`                                 |
| `ingress.tls`                              | Ingress TLS configuration                     | `[]`                                                       |
| `resources.fluentd.cpu`                    | CPU resource for fluentd                      | `400m`                                                     |
| `resources.fluentd.memory`                 | Memory resource for fluentd                   | `300Mi`                                                    |
| `resources.eSync.cpu`                      | CPU resource for eSync                        | `1000m`                                                    |
| `resources.eSync.memory`                   | CPU resource for eSync                        | `2.8Gi`                                                    |
| `autoscaling.enabled`                      | Enable Autoscaling                            | `True`                                                     |
| `autoscaling.minReplicas`                  | Autoscaling minimum replicas                  | `1`                                                        |
| `autoscaling.maxReplicas`                  | Autoscaling maximum replicas                  | `10`                                                       |
| `autoscaling.targetCPUUtilizationPercentage`| Autoscaling CPU utilization target percentage | `30`                                                      |
| `strategy.type`                            | Method for pod recreation                     | `Recreate`                                                 |
| `strategy.rollingUpdate.maxUnavailable`    | Max number of replicas unavailable            | `1`                                                        |
| `strategy.rollingUpdate.maxSurge`          | Max number of replicas to be brought          | `1`                                                        |
| `nodeSelector`                             | Selectes node with label                      | `{}`                                                       |
| `tolerations`                              | Toleration                                    | `[]`                                                       |
| `affinity`                                 | Affinity                                      | `{}`                                                       |
| `annotations`                              | Annotations                                   | ``                                                         |
| `imageCredentials.registry`                | Image Credentials registry                    | `docker.io`                                                |
| `imageCredentials.username`                | Image Credentials username                    | ``                                                         |
| `imageCredentials.password`                | Image Credentials password                    | ``                                                         |
| `imageCredentials.annotations`             | annotations (values are templated)            | ``                                                         |
| `imagePullSecrets.name`                    | Image Pull Secrets name                       | `windriver-test`                                           |
| `imageSecretName`                          | Image Seceret name                            | `windriver-test`                                           |
| `secretMountReload.name`                   | Secret mount reload name                      | `secret-mount-reload`                                      |
| `secretMountReload.extraSecretMounts.name` | Secret mount certificate name                 | `esync-certificate`                                        |
| `secretMountReload.extraSecretMounts.mountPath`| Secret mount certificate path                 | `/opt/soft/appshack/pki`                               |
| `secretMountReload.extraSecretMounts.secretName`| Secret mount certificate file name            | `esync-certificate`                                   |
| `secretMountReload.extraSecretMounts.readOnly`| Secret mount certificate permission           | `true`                                                  |
| `secretMountReload.extraSecretMounts.name` | Secret mount certificate name                 | `esync-certificate-ca`                                     |
| `secretMountReload.extraSecretMounts.mountPath`| Secret mount certificate ca path              | `/opt/soft/appshack/pki/ca`                            |
| `secretMountReload.extraSecretMounts.secretName`| Secret mount certificate ca file name         | `esync-certificate-ca`                                |
| `secretMountReload.extraSecretMounts.readOnly`| Secret mount certificate permission           | `true`                                                  |
| `secretKeys`                               | Add required secret keys under this label     | ``                                                         |
| `networkPolicy.enabled`                    | Enables network policy                        | `false`                                                    |
| `networkPolicy.ipBlock.cidr`               | Main CIDR range                               | `0.0.0.0/0`                                                |
| `networkPolicy.ipBlock.except_cidr`        | CIDR range not to allot for jms               | `**********/24`                                            |
| `networkPolicy.namespaceSelector.name`     | Name of namespace selector                    | `app`                                                      |
| `networkPolicy.namespaceSelector.value`    | Namespace selector                            | `esync-jms`                                                |
| `networkPolicy.podSelector.name`           | Name of pod selector                          | `app`                                                      |
| `networkPolicy.podSelector.value`          | Pod selector                                  | `esync-jms-chart`                                          |
| `pdb.minAvailable`                         | Minimum available pod for pbd                 | `1`                                                        |
| `pdb.annotations`                          | pbd annotations under this label              | ``                                                         |
| `rbac.annotations`                         | rbac annotations under this label             | ``                                                         |
| `dbMigration.enabled`                      | Enable database migration                     | `true`                                                     |
| `dbMigration.image.repository`             | Database migration image repository           | `excelfore/esync`                                          |
| `dbMigration.image.pullPolicy`             | Database migration image pull policy          | `IfNotPresent`                                             |
| `dbMigration.image.tag`                    | Database migration image tag                  | `core-master-latest-migration-all`                         |
| `redis.runAsContainer`                     | Run Redis as a container                      | `true`                                                     |
| `redis.resources`                          | Redis resource requirements                   | `{}`                                                       |
| `domain`                                   | Domain name                                   | `esync-test.excelfore.com`                                 |
| `snapstore.snap_url_protocol`              | Protocol for snap URL                         | `https`                                                    |
| `snapstore.device_port_max_threads`        | Max threads for device port                   | `10`                                                       |
| `snapstore.UI_port_max_threads`            | Max threads for UI port                       | `10`                                                       |
| `serverxml.oma.enable`                     | Enable OMA server                             | `true`                                                     |
| `serverxml.oma.ssl`                        | Enable SSL for OMA server                     | `true`                                                     |
| `serverxml.oma.port_max_threads`           | Max threads for OMA port                      | `150`                                                      |
| `serverxml.snap.enable`                    | Enable snap server                            | `true`                                                     |
| `serverxml.snap.ssl`                       | Enable SSL for snap server                    | `false`                                                    |
| `serverxml.snap.port_max_threads`          | Max threads for snap port                     | `150`                                                      |
| `opts.snapstore_workflow.campaign_approver_enable` | Enable campaign approver              | `false`                                                    |
| `opts.snapstore_workflow.package_approver_enable`  | Enable package approver               | `false`                                                    |
| `opts.custom_configjson_configurations.enable` | Enable custom config JSON                 | `true`                                                     |
| `opts.custom_configjson_configurations.configs` | Custom config JSON settings              | Various settings                                           |
| `terminationGracePeriod.enabled`           | Enable termination grace period               | `true`                                                     |
| `terminationGracePeriod.seconds`           | Termination grace period in seconds           | `60`                                                       |
| `signoz_status`                            | Enable SignOZ monitoring                      | `false`                                                    |
| `otel_status`                              | Enable OpenTelemetry                          | `false`                                                    |
| `otelHandler.repository`                   | OpenTelemetry handler repository              | `container-registry-internal.excelfore.com/excelfore/cap`  |
| `otelHandler.pullPolicy`                   | OpenTelemetry handler pull policy             | `IfNotPresent`                                             |
| `otelHandler.tag`                          | OpenTelemetry handler tag                     | `fetch-otel`                                               |
| `scheduledScaling.enabled`                 | Enable scheduled scaling                      | `false`                                                    |
| `scheduledScaling.scaleUp`                 | Scale up configuration                        | Time-based configuration                                   |
| `scheduledScaling.scaleDown`               | Scale down configuration                      | Time-based configuration                                   |
| `istio.enabled`                            | Enable Istio integration                      | `false`                                                    |


# To Run The Test 

## Install below Plugin
```console
helm plugin install https://github.com/quintush/helm-unittest
```

## To Generate Report
```console
helm unittest -f ./tests/deployment/deployment_test.yaml -f ./tests/cron/cron_test.yml -f ./tests/hpa/hpa_test.yml -f ./tests/network-policy/network-policy_test.yaml -f ./tests/pdb/pdb_test.yml -f ./tests/rbac/cluster_role_binding_test.yml -f ./tests/rbac/cluster-role_test.yml -f ./tests/schedulde-scaling/scaledown_test.yml -f ./tests/schedulde-scaling/scaleup_test.yml -f ./tests/secret/esync-secure-keys_test.yml -f ./tests/secret/secret_test.yml -f ./tests/service/service_test.yaml -f ./tests/serviceaccount/serviceaccount_test.yml -f ./tests/ingress/ingress.yaml -f ./tests/esync-virtual-service/esync-vs.yaml -f ./tests/esync-gateway/esync-gateway.yaml -f ./tests/config-map/config-map.yaml --color --debug --output-type JUnit --output-file reports/output.xml .
npm install junit-viewer -g
junit2html reports/output.xml reports/test.html
```
