{{- if .Values.scheduledScaling.enabled }}
{{- $namespace := .Values.namespace -}}
{{- $deployment := include "esync-server-chart.fullname" .  -}}
{{- $serviceAccount := include "esync-server-chart.serviceAccountName" .  -}}
{{- range .Values.scheduledScaling.scaleDown }}
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: "{{ .name }}-scaledown"
  namespace: {{ $namespace }}
  annotations:
     checkov.io/skip1: CKV_K8S_40=Containers should run as a high UID to avoid host conflict 
     checkov.io/skip2: CKV_K8S_14=Image Tag should be fixed - not latest or blank
     checkov.io/skip3: CKV_K8S_43=Image should use digest 
spec:
  schedule: {{ .time }}
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1 
  jobTemplate:
    spec:
      template:
        metadata:
          annotations:
            namespace: {{ $namespace }}
            seccomp.security.alpha.kubernetes.io/pod: "runtime/default"
        spec:
          securityContext:
              fsGroup: 1001        
          restartPolicy: OnFailure 
          automountServiceAccountToken: true           
          containers:
          - name: kubectl
            image: bitnami/kubectl:latest
            resources:
              requests:
                  cpu: 100m
                  memory: 100Mi              
              limits:
                  cpu: 100m
                  memory: 100Mi            
            securityContext:  
              privileged: false
              allowPrivilegeEscalation: false
              capabilities:
                drop:
                - ALL
              runAsNonRoot: true
              runAsUser: 1001
              readOnlyRootFilesystem: true        
            imagePullPolicy: Always           
            args: 
              - scale
              - --replicas={{ .replica }}
              - deployment.apps/{{ $deployment }}
              - -n
              - {{ $namespace }}
          serviceAccountName: {{ $serviceAccount }}        
{{- end }}
{{- end }}