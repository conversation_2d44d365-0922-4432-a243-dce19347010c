---
{{- if .Values.pdb.minAvailable }}
apiVersion: policy/v1beta1
kind: PodDisruptionBudget
metadata:
  name: {{ include "esync-server-chart.fullname" . }}
  {{- with .Values.pdb.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}   
spec:
  minAvailable: {{ .Values.pdb.minAvailable }}
  selector:
    matchLabels:
      app: "{{ include "esync-server-chart.fullname" . }}"
{{- end }}