apiVersion: apps/v1
kind: Deployment
metadata:
{{- with .Values.podAnnotations }}
  annotations:
    {{- toYaml . | nindent 8 }}
{{- end }}
  namespace: {{ .Values.namespace }} 
  name: {{ include "esync-server-chart.fullname" . }}
  labels:
    {{- include "esync-server-chart.labels" . | nindent 4 }}
spec:
{{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
{{- end }}
{{- if eq .Values.strategy.type "RollingUpdate" }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: {{ .Values.strategy.rollingUpdate.maxUnavailable }}
      maxSurge: {{ .Values.strategy.rollingUpdate.maxSurge }}
{{- else if eq .Values.strategy.type "Recreate" }}
  strategy:
    type: Recreate
{{- end }}
  selector:
    matchLabels:
      {{- include "esync-server-chart.selectorLabels" . | nindent 6 }}
  template:
    metadata:
    {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      labels:
        {{- include "esync-server-chart.selectorLabels" . | nindent 8 }}
    spec:
      {{- if .Values.terminationGracePeriod.enabled }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriod.seconds }}
      {{- end }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}    
      serviceAccountName: {{ include "esync-server-chart.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      initContainers:
      {{- if .Values.dbMigration.enabled }}
        - name: esync-db-migration
          image: "{{ .Values.dbMigration.image.repository }}:{{ .Values.dbMigration.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.dbMigration.image.pullPolicy }}
          command: ["/bin/sh","-c"]
          args:
          - /xl4migration/entrypoint.sh
          resources:
            {{- toYaml .Values.dbMigration.resources | nindent 12 }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          env:
            - name: "xl4.appshack.cmd.fluentd.port"
              value: "{{ .Values.fluentd.port }}"
            - name: MY_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: MY_POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: MY_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: MY_POD_SERVICE_ACCOUNT
              valueFrom:
                fieldRef:
                  fieldPath: spec.serviceAccountName
          #{{- if not .Values.serviceAccount.create }}
          #  - name: RDS_USER_NAME
          #    valueFrom:
          #      secretKeyRef:
          #        name: aws-esync-secret-rds
          #        key: username
          #  - name: RDS_PASSWORD
          #    valueFrom:
          #      secretKeyRef:
          #        name: aws-esync-secret-rds
          #        key: password
          #{{- end }}
          volumeMounts:
          {{- range .Values.secretMountReload.extraSecretMounts }}
            - name: {{ $.Values.secretMountReload.name }}-{{ .secretName }}
              mountPath: {{ .mountPath }}
              readOnly: {{ .readOnly }}
          {{- end }}
          {{- range .Values.configmapReload.extraConfigmapMounts }}
            - name: {{ $.Values.configmapReload.name }}-{{ .name }}
              mountPath: {{ .mountPath }}
              subPath: {{ .subPath }}
          {{- end }}
            - name: fluentd-config-map
              mountPath: /fluentd/etc/fluentxl4.conf
              subPath: fluentxl4.conf
            - name: configmap-snapstore-config
              mountPath: /snapstore.config
              subPath: snapstore.config
            - name: configmap-appshack-logging-properties
              mountPath: /logging.properties
              subPath: logging.properties
        {{- end }}
	{{- if .Values.fluentdHandler.enabled }}
        - name: fetch
          image: "{{ .Values.fluentdHandler.repository }}:{{ .Values.fluentdHandler.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          resources:
              requests:
                  cpu: 100m
                  memory: 0.2Gi
              limits:
                  cpu: 100m
                  memory: 0.2Gi
          command: ["/bin/sh","-c"]
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}           
          args: ["mkdir -p /fluentd-entrypoint-init; cp /fluentd/fluentd-jdk-handler.jar /fluentd-entrypoint-init/"]
          volumeMounts:
            - mountPath: /fluentd-entrypoint-init
              name: dump
	{{- end }}
        {{- if eq .Values.otel_status true }}
        - name: fetch-otel
          image: "{{ .Values.otelHandler.repository }}:{{ .Values.otelHandler.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          resources: {}
          command: ["/bin/sh","-c"]
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          args: ["mkdir -p /otel-entrypoint-init ; cp /signoz/opentelemetry-javaagent.jar /otel-entrypoint-init/"]
          volumeMounts:
            - mountPath: /otel-entrypoint-init
              name: oteldump
        {{- end }}
        - name: fetch-jar
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          image: "{{ .Values.jmxConfiguration.repository }}:{{ .Values.jmxConfiguration.tag | default .Chart.AppVersion }}"
          command: ["/bin/sh","-c"]
          resources:
              requests:
                  cpu: 100m
                  memory: 0.2Gi
              limits:
                  cpu: 100m
                  memory: 0.2Gi
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}           
          args: ["mkdir -p /jmx-entrypoint-init; cp /jmx/jmx_prometheus_javaagent-0.12.0.jar /jmx-entrypoint-init/; cp /jmx/jmx-config.yml /jmx-entrypoint-init/"]
          volumeMounts:
            - mountPath: /jmx-entrypoint-init
              name: jmxdump
      containers:
      {{- if .Values.redis.runAsContainer }}
        - name: esync-redis
          image: "redis/redis-stack-server:latest"
          imagePullPolicy: Always
          resources:
            {{- toYaml .Values.redis.resources | nindent 12 }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          volumeMounts:
            - mountPath: /data
              name: redis-data
      {{- end }}
        - image: "{{ .Values.fluentd.repository }}:{{ .Values.fluentd.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["/bin/sh","-c"]
          args:
          - fluentd -c /fluentd/etc/fluentxl4.conf -p /fluentd/plugins --gemfile /fluentd/Gemfile;      
          name: fluentd
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          ports:
          - containerPort: {{ .Values.fluentd.port }}
          {{- if .Values.resources.enabled }}
          resources:
              requests:
                  cpu: {{ .Values.resources.fluentd.cpu }}
                  memory: {{ .Values.resources.fluentd.memory }}
              limits:
                  cpu: {{ .Values.resources.fluentd.cpu }}
                  memory: {{ .Values.resources.fluentd.memory }}
          {{- else }}
          resources: {}
          {{- end }}
          volumeMounts:       
          - name: fluentd-config-map
            mountPath: /fluentd/etc/fluentxl4.conf
            subPath: fluentxl4.conf 
          - mountPath: "/usr/local/tomcat/logs"
            name: logs-pvc
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:                               
            - name: MY_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: MY_POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: MY_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: MY_POD_SERVICE_ACCOUNT
              valueFrom:
                fieldRef:
                  fieldPath: spec.serviceAccountName
          #{{- if not .Values.serviceAccount.create }}
          #  - name: RDS_USER_NAME
          #    valueFrom:
          #      secretKeyRef:
          #        name: aws-esync-secret-rds
          #        key: username
          #  - name: RDS_PASSWORD
          #    valueFrom:
          #      secretKeyRef:
          #        name: aws-esync-secret-rds
          #        key: password
          #  - name: AMQ_USER_NAME
          #    valueFrom:
          #      secretKeyRef:
          #        name: aws-esync-secret-amq
          #        key: username
          #  - name: AMQ_PASSWORD
          #    valueFrom:
          #      secretKeyRef:
          #        name: aws-esync-secret-amq
          #        key: password
          #  - name: TRUSTSTORE_PASSWORD
          #    valueFrom:
          #      secretKeyRef:
          #        name: aws-esync-snapstore
          #        key: truststore_password
          #  - name: CERTIFICATE_KEY_PASSWORD
          #    valueFrom:
          #      secretKeyRef:
          #        name: aws-esync-snapstore
          #        key: certificate_password
          #  - name: CERTIFICATE_KEYSTORE_PASSWORD
          #    valueFrom:
          #      secretKeyRef:
          #         name: aws-esync-snapstore
          #         key: keystore_password
          #{{- end }}
          {{- range .Values.secretKeys }}
            - name: {{ .name | upper }}
              valueFrom:
                secretKeyRef:
                  name: esync-secure-keys
                  key: {{ .name }}
          {{- end }}           
          ports:
          {{- range $k, $v := .Values.service.endpoints }}
          - containerPort: {{ $v.port }}
          {{- end }}
          {{- if .Values.resources.enabled }}
          resources:
              requests:
                  cpu: {{ .Values.resources.eSync.cpu }}
                  memory: {{ .Values.resources.eSync.memory }}
              limits:
                  cpu: {{ .Values.resources.eSync.cpu }}
                  memory: {{ .Values.resources.eSync.memory }}
          {{- else }}
          resources: {}
          {{- end }}
          {{- if .Values.startupProbe.enabled }}
          startupProbe:
            {{- with .Values.startupProbe.exec }}
            exec:
              {{- toYaml . | nindent 14 }}
            {{- end }}
            initialDelaySeconds: {{ .Values.startupProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.startupProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.startupProbe.timeoutSeconds }}
            successThreshold: {{ .Values.startupProbe.successThreshold }}
            failureThreshold: {{ .Values.startupProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: /snap/health?source=uO5wW3yR7nN2jG4v
              port: {{ .Values.service.endpoints.snap.targetPort }}
          {{- if .Values.serverxml.snap.ssl }}
              scheme: HTTPS
          {{- end }}
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.readinessProbe.successThreshold }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}            
          {{- end }}
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: /snap/health?source=uO5wW3yR7nN2jG4v
              port: {{ .Values.service.endpoints.snap.targetPort }}
          {{- if .Values.serverxml.snap.ssl }}
              scheme: HTTPS
          {{- end }}              
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}            
          {{- end }}
          volumeMounts:
          - name: configmap-snapstore-config
            mountPath: /usr/local/tomcat/conf/snapstore.config
            subPath: snapstore.config
          - name: configmap-snapstore-workflow-xml
            mountPath: /usr/local/tomcat/conf/snapstore_workflow.xml
            subPath: snapstore_workflow.xml
          - name: configmap-appshack-logging-properties
            mountPath: /usr/local/tomcat/conf/logging.properties
            subPath: logging.properties      
          - name: configmap-demo-device-groups-config
            mountPath: /usr/local/tomcat/conf/demo_device_groups.xml
            subPath: demo_device_groups.xml
          - name: configmap-quartz-xl4-properties
            mountPath: /usr/local/tomcat/conf/quartz_xl4.properties
            subPath: quartz_xl4.properties    
          - name: configmap-server-xl4
            mountPath: /usr/local/tomcat/conf/server.xml
            subPath: server.xml
          - name: appshack-static-config-mount
            mountPath: /usr/local/tomcat/conf/static/assets/sotauiv4/config.json 
            subPath: config.json
          - name: configmap-setenv-sh
            mountPath: /usr/local/tomcat/bin/setenv.sh
            subPath: setenv.sh
          {{- if .Values.fluentdHandler.enabled }} 
          - mountPath: /fluentd-entrypoint-init
            name: dump
          {{- end }}
          {{- if eq .Values.otel_status true}}
          - mountPath: /otel-entrypoint-init
            name: oteldump
          {{- end }}
          - mountPath: /jmx-entrypoint-init
            name: jmxdump
          - name: configmap-acl-device-xml
            mountPath: /usr/local/tomcat/conf/acl-device.xml
            subPath: acl-device.xml
        {{- range .Values.configmapReload.extraConfigmapMounts }}
          - name: {{ $.Values.configmapReload.name }}-{{ .name }}
            mountPath: {{ .mountPath }}
            subPath: {{ .subPath }}
        {{- end }}
        {{- range .Values.secretMountReload.extraSecretMounts }}
          - name: {{ $.Values.secretMountReload.name }}-{{ .secretName }}
            mountPath: {{ .mountPath }}
            readOnly: {{ .readOnly }}
        {{- end }}
          - mountPath: "/usr/local/tomcat/logs"
            name: logs-pvc 
        #{{- if not .Values.serviceAccount.create }}
        #  - name: secrets-store-inline
        #    mountPath: /home/<USER>
        #    readOnly: true
        #{{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
      - name: fluentd-config-map
        configMap:
          name: fluentd-config-map
      {{- if .Values.fluentdHandler.enabled }}
      - emptyDir: {}
        name: dump
      {{- end }}
      - emptyDir: {}
        name: redis-data
      - emptyDir: {}
        name: jmxdump
      - emptyDir: {}
        name: oteldump
    {{- range .Values.secretMountReload.extraSecretMounts }}
      - name: {{ $.Values.secretMountReload.name }}-{{ .secretName }}
        secret:
          secretName: {{ .secretName }}
    {{- end }}
      - name: logs-pvc
        emptyDir: {}
      - name: configmap-snapstore-config
        configMap:
          name: configmap-snapstore-config
      - name: configmap-snapstore-workflow-xml
        configMap:
          name: configmap-snapstore-workflow-xml
      - name: configmap-appshack-logging-properties
        configMap:
          name: configmap-appshack-logging-properties
      - name: configmap-demo-device-groups-config
        configMap:
          name: configmap-demo-device-groups-config
      - name: configmap-quartz-xl4-properties
        configMap:
          name: configmap-quartz-xl4-properties
      - name: configmap-server-xl4
        configMap:
          name: configmap-server-xl4
      - name: appshack-static-config-mount
        configMap:
          name: appshack-static-config-mount
      - name: configmap-setenv-sh
        configMap:
          name: configmap-setenv-sh
      - name: configmap-acl-device-xml
        configMap:
          name: configmap-acl-device-xml
    {{- range .Values.configmapReload.extraConfigmapMounts }}
      - name: {{ $.Values.configmapReload.name }}-{{ .name }}
        configMap:
          name: {{ .configMap }}
    {{- end }}
    #{{- if not .Values.serviceAccount.create }}
    #  - name: secrets-store-inline
    #    csi:
    #      driver: secrets-store.csi.k8s.io
    #      readOnly: true
    #      volumeAttributes:
    #        secretProviderClass: "aws-secrets"
    #{{- end }}