eSync Server:
Thank you for installing {{ .Chart.Name }}.

Your release is named {{ .Release.Name }}.

{{ $omaport := .Values.service.endpoints.oma.port }}

{{- range .Values.ingress.hosts }}

If SSL enabled : 

FRONTEND URL : https://{{ .host }}/sotauiv4 # After domain pointing

If SSL disabled :

FRONTEND URL : http://{{ .host }}/sotauiv4 # After domain pointing


DEVICE URL : https://{{ .host }}:{{ $omaport }}/snap/oma # After domain pointing 
 
{{- end }}


To see details about the deployment, execute below command

kubectl --namespace={{ .Release.Namespace }} get all -l "app.kubernetes.io/name={{ template "esync-server-chart.name" . }}"
