{{- if .Values.cron.enabled }}
apiVersion: batch/v1
kind: CronJob
metadata:
  name: esync-psql-vaccume-analyser
spec:
  schedule: "{{ .Values.cron.cron_schedule }}"
  concurrencyPolicy: {{.Values.cron.concurrencyPolicy}}
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: vaccum-analyser-container
            image: "{{ .Values.cron.cron_image }}"
            imagePullPolicy: IfNotPresent
            command: [ "bin/sh", "-c", "PGPASSWORD={{.Values.rds.rds_db_password}}  psql --host={{.Values.rds.rds_db_url}} --port={{.Values.rds.rds_db_port}} --username={{.Values.rds.rds_db_user_name}} --dbname={{.Values.rds.rds_db_name}} -c 'VACUUM ANALYZE;'" ]
            env:
              - name: POSTGRESQL_USERNAME
                value: {{.Values.rds.rds_db_user_name}}
              - name: POSTGRESQL_PASSWORD
                value: {{.Values.rds.rds_db_password}}
              - name: POSTGRESQL_DATABASE
                value: {{.Values.rds.rds_db_name}}            
              - name: DB_HOST
                value: {{.Values.rds.rds_db_url}}
          restartPolicy: OnFailure
{{- end }}
