{{- if .Values.istio.enabled -}}
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: {{ include "esync-server-chart.fullname" . }}-virtual-service-ui
spec:
  hosts:
  - {{ .Values.domain }}
  gateways:
  - {{ include "esync-server-chart.fullname" . }}-ui
  tcp:
  - match:
    - port: {{ .Values.service.endpoints.snap.port }}
    route:
    - destination:
        host: "{{ include "esync-server-chart.fullname" . }}.{{ .Values.namespace }}.svc.cluster.local"
        port:
          number: {{ .Values.service.endpoints.snap.targetPort }}
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: {{ include "esync-server-chart.fullname" . }}-virtual-service-device
spec:
  hosts:
  - {{ .Values.domain }}
  gateways:
  - {{ include "esync-server-chart.fullname" . }}-device
  tcp:
  - match:
    - port: {{ .Values.service.endpoints.oma.port }}
    route:
    - destination:
        host: "{{ include "esync-server-chart.fullname" . }}.{{ .Values.namespace }}.svc.cluster.local"
        port:
          number: {{ .Values.service.endpoints.oma.targetPort }}
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: {{ include "esync-server-chart.fullname" . }}-virtual-service-tsp
spec:
  hosts:
  - {{ .Values.domain }}
  gateways:
  - {{ include "esync-server-chart.fullname" . }}-tsp
  tcp:
  - match:
    - port: {{ .Values.service.endpoints.tsp.port }}
    route:
    - destination:
        host: "{{ include "esync-server-chart.fullname" . }}.{{ .Values.namespace }}.svc.cluster.local"
        port:
          number: {{ .Values.service.endpoints.tsp.targetPort }}
{{- end }}
