apiVersion: v1
data:
  quartz_xl4.properties: |
    #============================================================================
    # Configure Main Scheduler Properties
    #============================================================================
    org.quartz.scheduler.instanceName = XL4scheduler
    org.quartz.scheduler.skipUpdateCheck = true
    org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool
    org.quartz.threadPool.threadCount = 5
    org.quartz.threadPool.threadPriority = 5
    org.quartz.scheduler.wrapJobExecutionInUserTransaction = false
    org.quartz.jobStore.misfireThreshold = 60000
    org.quartz.dataSource.DSNX.jndiURL = snap_db_jpa
    org.quartz.dataSource.DSNX.jndiAlwaysLookup = true
    org.quartz.jobStore.class = org.quartz.impl.jdbcjobstore.JobStoreTX
    org.quartz.jobStore.driverDelegateClass = org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
    org.quartz.jobStore.dataSource = DSNX
    org.quartz.jobStore.useProperties = true
    org.quartz.jobStore.isClustered = true
    org.quartz.scheduler.jmx.export = true
kind: ConfigMap
metadata:
  name: configmap-quartz-xl4-properties
  namespace: {{ .Values.namespace }}
