apiVersion: v1
data:
  snapstore.config: |
    snapstore.db.url = jdbc:postgresql://{{ .Values.rds.rds_db_url}}:{{ .Values.rds.rds_db_port }}/{{ .Values.rds.rds_db_name }}?tcp_keepalives_idle=5&tcp_keepalives_interval=5&tcp_keepalives_count=3
    snapstore.db.user := {{ .Values.rds.rds_db_user_name }}
    snapstore.db.password := {{ .Values.rds.rds_db_password }}
    snapstore.db.pool_max_active = {{ .Values.rds.pool_max_active }}
    snapstore.db.pool_min_idle = {{ .Values.rds.pool_min_idle }}
    snapstore.db.pool_max_idle = {{ .Values.rds.pool_max_idle }}
    snapstore.postgres.32bit = true
    snapstore.workflow_config = $/snapstore_workflow.xml
    snapstore.session_timeout = 86400
    snapstore.snap_url = {{- include "snap-url-generator" .  }}
    snapstore.oma_url = https://{{ .Values.domain }}:{{ .Values.service.endpoints.oma.port}}/snap/
    snapstore.device_traffic_port = {{ .Values.service.endpoints.oma.port }}
    snapstore.device_auth.rdn.common = OU
    snapstore.device_auth.common_name = xl4_device
    snapstore.device_auth.enable_boota = false
    snapstore.device_auth.ocsp = DISABLED
    snapstore.device_auth.ocsp_timeout = 15000
    snapstore.postgres.regconfig_query = SYSTEM
    snapstore.postgres.regconfig_vector = SYSTEM
    snapstore.account_device_db_map = default=demodb:XL4
    snapstore.device_db_impl = demodb:com.excelfore.api.impl.DemoDeviceDatabase
    snapstore.api.impl.clustering = com.excelfore.api.impl.ApacheTribesCluster
    snapstore.tribes_receiver_port = 4100
    snapstore.tribes_membership_service = org.apache.catalina.tribes.membership.cloud.CloudMembershipService
    snapstore.tribes_membership_service.membershipProviderClassName = kubernetes
    snapstore.clustering.sync_response_timeout_ms = 5000
    snapstore.sso.config.okta = com.excelfore.api.impl.SSOOkta
    snapstore.sso.config.demo = com.excelfore.api.impl.sso.demo.Impl
    snapstore.sso.config.ssl = com.excelfore.api.impl.sso.ssl.SSL_SSO
    snapstore.sso.config.saml = com.excelfore.api.impl.sso.saml.SAML
    snapstore.api.impl.sso = {{ .Values.snapstore.login_type }}
    snapstore.sso.config.demo = com.excelfore.api.impl.sso.demo.Impl
    snapstore.demo_sso.demo.excelfore.password = {{ .Values.snapstore.demo_login_password }}
    snapstore.demo_sso.demo.excelfore.display = {{ .Values.snapstore.demo_login_user }}
    snapstore.demo_sso.demo.excelfore.tenancy = default
    snapstore.demo_sso.demo.excelfore.role = {{ .Values.snapstore.demo_login_role }}
    snapstore.campaign_manager_job_cron_expression = 0 * * * * ?
    snapstore.hardware_update_job_cron_expression = 0 0 0 * * ?
    snapstore.campaign_manager_run_on_deployed = true
    snapstore.campaign_manager_max_devices = 10
    snapstore.campaign_manager_min_vehicles_day = 10
    snapstore.campaign_manager_abort_batch_size = 1000
    snapstore.campaign_manager_tx_sec_per_device = 1
    snapstore.campaign_manager_tx_sec_min_timeout = 30
    snapstore.campaign_manager_remove_updates = false
    snapstore.ui.oma.enable_deleting_campaigns = false
    snapstore.ui.oma.enable_to_rerun_campaigns = false
    snapstore.oma.check_device_vin = false
    snapstore.oma.download_connect_timeout_ms = 10000
    snapstore.oma.download_read_timeout_ms = 10000
    snapstore.oma.download_skip_timeout_ms = 50
    snapstore.oma.oma_read_timeout_ms = 10000
    snapstore.oma.max_update_retries = 3
    snapstore.ui.oma.show_campaign_missing_hardware = true
    snapstore.ui.oma.max_page_size = 2000
    snapstore.ui.oma.max_items_size = 1000
    snapstore.crypto.key_size = 128
    snapstore.crypto.method = AES/CTR/NoPadding
    snapstore.signature_cert_chain := {{ .Values.snapstore.certificates.signature_cert_chain_path }}   
    snapstore.signature_private_key := {{ .Values.snapstore.certificates.signature_private_key_path }}
    snapstore.signature_private_key_password := {{ .Values.snapstore.certificates.signature_private_key_password }} 
    snapstore.signature_trust_store := {{ .Values.snapstore.certificates.signature_trust_store_path }}
    snapstore.signature_trust_store_password := {{ .Values.snapstore.certificates.signature_trust_store_password }}
    snapstore.server_signature_trust_store := {{ .Values.snapstore.certificates.server_signature_trust_store_path }}
    snapstore.server_signature_trust_store_password := {{ .Values.snapstore.certificates.server_signature_trust_store_password }}
    snapstore.server_signature_has_sign_auth = true
    snapstore.signer_auth.common_name = xl4_developer
    snapstore.signer_server.common_name = xl4_server
    snapstore.transient_resign_threshold = 10000000
    snapstore.device_auth.sota_oid = 1.0.15961.13.96, *******.4.1.45473.1.10
    snapstore.sota_download.dump_binary = false
    snapstore.sota_download.response_buffer_size = 16384
    snapstore.sota_download.encrypt = true
    snapstore.monitoring.cool_off_sec = 30
    snapstore.monitoring.repost_each_sec = 60
    snapstore.monitoring.max_queue = 1000
    snapstore.monitoring.heartbeat_frequency = 30
    snapstore.pretty_json = false
    snapstore.api.impl.certificate_control = com.excelfore.api.impl.NoCertificateControl
    snapstore.api.impl.alerter = 
    snapstore.control_admin.password = {{ .Values.snapstore.admin_password }}
    snapstore.control_admin.timeout = 86400
    snapstore.performance.max_op_time = 5000
    snapstore.performance.debug_time = false
    snapstore.logic.preserve_archived_trail = true
    snapstore.max_bin_processors = 20
    snapstore.cors.max_age = 151200
    snapstore.cors.headers = 
    snapstore.diagnostics.max_log = 2000
    snapstore.diagnostics.store = false
    snapstore.binary_service.allowed_mime_types = application/zip, application/pdf
    snapstore.binary_service.allow_mime_type_override = false
    snapstore.http.no_chunked = false
    snapstore.http.ranged_download = ENABLED
    snapstore.ui.import.vin.max_error_count_to_return = 100
    snapstore.duplicate_message_remove_job_cron_expression = 0 0 0 * * ?
    snapstore.binary_remove_cron_expression = 0 0 * * * ? 
    snapstore.csrf.timeout_min = 10
    snapstore.excelfore.health.check.password = $2a$10$NE.9kBrwNsmrwEyJR73CfO7KqmPvpI5VTSAYRdOBOs8mrKWRwX7R.
    snapstore.demo_db.secure_port = {{ .Values.service.endpoints.tsp.targetPort }}
    snapstore.http.no_chunked = false
    snapstore.delta.max_mem = 157286400
    snapstore.delta.cluster_check_interval_sec = 300
    snapstore.delta.difference = com.excelfore.api.impl.DeltaBsdiff,com.excelfore.api.impl.DeltaVcdiff,com.excelfore.api.impl.ScriptDiff
    snapstore.delta.script_list = /opt/soft/appshack/esdiffserver
    snapstore.ssl_sso.ssl.username = %attr:*******.4.1.45473.1.4%
    snapstore.ssl_sso.ssl.tenancy = %attr:*******.4.1.45473.1.5%
    snapstore.ssl_sso.ssl.roles = %attr:********%
    snapstore.ssl_sso.ssl.server = esync_sso
    snapstore.ssl_sso.ssl.display = %attr:2.16.840.1.113730.3.1.241%
    snapstore.vcc.collection_timeout_ms = 5000 
    {{- include "custom_snapstore_config_key" .  }}
    {{- include "redis-configuration-generator" .  }}
kind: ConfigMap
metadata:
  name: configmap-snapstore-config
  namespace: {{ .Values.namespace }}
