apiVersion: v1
data:
  logging.properties: |
    handlers = codes.vps.logging.fluentd.jdk.FluentdHandler
    .handlers = codes.vps.logging.fluentd.jdk.FluentdHandler
    com.excelfore.handlers = codes.vps.logging.fluentd.jdk.FluentdHandler
    codes.vps.logging.fluentd.jdk.FluentdHandler.port = {{.Values.fluentd.port}}
    codes.vps.logging.fluentd.jdk.FluentdHandler.tag_prefix = fluentd
    codes.vps.logging.fluentd.jdk.FluentdHandler.format = level"${level}";class"${class}";sequence"${sequence}";thread_id"[${tid}]";method"${class}.${method}";message"${message}";stack"${trace}";params"${params}";nanos"${nanos}";logger"${logger}";namespace"$[MY_POD_NAMESPACE]";pod_name"$[MY_POD_NAME]";node_name"$[MY_NODE_NAME]";pod_ip"$[MY_POD_IP]";service_account"$[MY_POD_SERVICE_ACCOUNT]";tomcat_version"$[TOMCAT_VERSION]";
    org.apache.catalina.core.ContainerBase.[Catalina].[localhost].level = DEBUG
    org.apache.catalina.core.ContainerBase.[Catalina].[localhost].handlers = codes.vps.logging.fluentd.jdk.FluentdHandler

kind: ConfigMap
metadata:
  name: configmap-appshack-logging-properties
  namespace: {{ .Values.namespace }}
