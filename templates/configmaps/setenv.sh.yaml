apiVersion: v1
data:
  setenv.sh: |
    #!/bin/sh
    export CLASSPATH="$CLASSPATH":"/fluentd-entrypoint-init/fluentd-jdk-handler.jar"
    export KUBERNETES_NAMESPACE={{ .Values.namespace }}
    export JAVA_OPTS="{{- include "custom_javaopts_configurations" .  }}"
    {{- if eq  .Values.opts.custom_catalinaopts_configurations.enable true }}
    export CATALINA_OPTS="{{- include "custom_catalinaopts_configurations" .  }}"
    {{- end }}
    {{- if or (eq .Values.signoz_status true) (and (eq .Values.signoz_status false) (eq .Values.otelopts_status true)) }}
    export JAVA_OPTS="{{- include "custom_otelopts_configurations" . }} $JAVA_OPTS"
    {{- end }}

kind: ConfigMap
metadata:
  name: configmap-setenv-sh
  namespace: {{ .Values.namespace }}
