apiVersion: v1
data:
  snapstore_workflow.xml: |
    <?xml version="1.0" encoding="UTF-8"?>
    <workflowConfiguration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                        xsi:noNamespaceSchemaLocation="src/java/workflow.xsd">
        <permission>
            <operation>SOTA_CONTROL</operation>
            <roles>SYSTEM ADMIN</roles>
            <roles>OTA ADMIN</roles>
        </permission>
       {{- if .Values.opts.snapstore_workflow.campaign_approver_enable }}
        <permission>
            <operation>SOTA_COMPONENT_ACCOUNT_EDIT</operation>
            <roles>OTA ADMIN</roles>
        </permission>
       {{- end }}
        <permission>
            <operation>SOTA_COMPONENT_ACCOUNT_READ</operation>
            <roles>OTA ADMIN</roles>
            <roles>READ ONLY</roles>
            <roles>PACKAGE ASSEMBLER</roles>
            <roles>CAMPAIGN MANAGER</roles>
        </permission>
        <permission>
            <operation>SOT<PERSON>_DEVELOPER</operation>
            <roles>OTA ADMIN</roles>
        </permission>
        <permission>
            <operation>SOTA_COMPONENT_ALL_READ</operation>
            <roles>OTA ADMIN</roles>
        </permission>
        <permission>
            <operation>SOTA_COMPONENT_OWN_READ</operation>
            <roles>COMPONENT DEVELOPER</roles>
            <roles>OTA ADMIN</roles>
        </permission>
        <permission>
            <operation>SOTA_COMPONENT_ACCOUNT_CREATE</operation>
            <roles>OTA ADMIN</roles>
            <roles>COMPONENT DEVELOPER</roles>
        </permission>
        <permission>
            <operation>SOTA_COMPONENT_ACCOUNT_DELETE</operation>
            <roles>OTA ADMIN</roles>
            <roles>COMPONENT DEVELOPER</roles>
        </permission>
        <permission>
            <operation>SOTA_COMPONENT_ALL_SIGN</operation>
            <roles>OTA ADMIN</roles>
        </permission>   
        <permission>
            <operation>SOTA_COMPONENT_ACCOUNT_EDIT</operation>
            <roles>OTA ADMIN</roles>
        </permission>
        <permission>
            <operation>SOTA_COMPONENT_ALL_DELETE</operation>
            <roles>OTA ADMIN</roles>
        </permission>
        <permission>
            <operation>SOTA_COMPONENT_OWN_DELETE</operation>
            <roles>OTA ADMIN</roles>
        </permission>
        <permission>
            <operation>SOTA_PACKAGES_ACCOUNT_READ</operation>
            <roles>READ ONLY</roles>
            <roles>OTA ADMIN</roles>
            <roles>PACKAGE ASSEMBLER</roles>
            <roles>CAMPAIGN MANAGER</roles>
            {{- if .Values.opts.snapstore_workflow.package_approver_enable }}
            <roles>PACKAGE APPROVER</roles>
            {{- end }}
        </permission>
        <permission>
            <operation>SOTA_PACKAGES_ACCOUNT_WRITE</operation>
            <roles>OTA ADMIN</roles>
            <roles>PACKAGE ASSEMBLER</roles>
            {{- if .Values.opts.snapstore_workflow.package_approver_enable }}
            <roles>PACKAGE APPROVER</roles>
            {{- end }}
        </permission>
        <permission>
            <operation>SOTA_PACKAGES_ACCOUNT_DELETE</operation>
            <roles>OTA ADMIN</roles>
            <roles>PACKAGE ASSEMBLER</roles>
            {{- if .Values.opts.snapstore_workflow.package_approver_enable }}
            <roles>PACKAGE APPROVER</roles>
            {{- end }}
        </permission>
        <permission>
            <operation>SOTA_CAMPAIGN_ACCOUNT_READ</operation>
            <roles>READ ONLY</roles>
            <roles>OTA ADMIN</roles>
            <roles>CAMPAIGN MANAGER</roles>
        </permission>
        <permission>
            <operation>SOTA_CAMPAIGN_ACCOUNT_WRITE</operation>
            <roles>OTA ADMIN</roles>
            <roles>CAMPAIGN MANAGER</roles>
            {{- if .Values.opts.snapstore_workflow.campaign_approver_enable }}
            <roles>CAMPAIGN APPROVER</roles>
            {{- end }}
        </permission>
        <permission>
            <operation>SOTA_CAMPAIGN_ACCOUNT_DEPLOY</operation>
            <roles>OTA ADMIN</roles>
            <roles>CAMPAIGN MANAGER</roles>
            {{- if .Values.opts.snapstore_workflow.campaign_approver_enable }}
            <roles>CAMPAIGN APPROVER</roles>
            {{- end }}
        </permission>
        <permission>
            <operation>SOTA_CAMPAIGN_ACCOUNT_DELETE</operation>
            <roles>OTA ADMIN</roles>
            <roles>CAMPAIGN MANAGER</roles>
            {{- if .Values.opts.snapstore_workflow.campaign_approver_enable }}
            <roles>CAMPAIGN APPROVER</roles>
            {{- end }}
        </permission>
        <permission>
            <operation>SOTA_DEVICE_GROUP_INFORMATION</operation>
            <roles>OTA ADMIN</roles>
            <roles>READ ONLY</roles>
            <roles>DIAGNOSTICS MANAGER</roles>
            <roles>CAMPAIGN MANAGER</roles>
        </permission>
        <permission>
            <operation>SOTA_DEVICE_GROUP_UPDATE</operation>
            <roles>OTA ADMIN</roles>
        </permission>
        <permission>
            <operation>SOTA_USER_MANAGEMENT</operation>
            <roles>USER MANAGEMENT</roles>
            <roles>OTA ADMIN</roles>
        </permission>
        <permission>
            <operation>SOTA_SUBSCRIBER</operation>
            <roles>CAMPAIGN MANAGER</roles>
            <roles>OTA ADMIN</roles>
        </permission>
        <permission>
            <operation>SOTA_CDB_ACCOUNT</operation>
            <roles>OTA ADMIN</roles>
            <roles>CDB MANAGER</roles>
        </permission>
        <permission>
            <operation>SOTA_CDB_ALL</operation>
            <roles>OTA ADMIN</roles>
        </permission>
        <permission>
        <operation>SOTA_CDB_INTAKE_ACCOUNT</operation>
            <roles>OTA ADMIN</roles>
            <roles>CDB MANAGER</roles>
        </permission>
        <permission>
            <operation>SOTA_CDB_INTAKE_ALL</operation>
            <roles>OTA ADMIN</roles>
        </permission>
        {{- if .Values.opts.snapstore_workflow.campaign_approver_enable }}
        <sotaWorkflowStage name="campaign_init" allowEdits="true">
            <target name="submit for campaign workflow" operation="submit" stage="campaign_approve">
                <submitters byOwner="true"/>
            </target>
        </sotaWorkflowStage>

        <sotaWorkflowStage name="campaign_approve">
            <target name="campaign approve it" operation="approve" stage="campaign_approved">
                <submitters>
                <role>OTA ADMIN</role>
                <role>CAMPAIGN APPROVER</role>
                </submitters>
            </target>
            <target name="campaign  deny it" operation="reject" stage="campaign_init">
                <submitters>
                <role>OTA ADMIN</role>
                <role>CAMPAIGN APPROVER</role>
                </submitters>
            </target>
        </sotaWorkflowStage>

        <sotaWorkflowStage name="campaign_approved" published="true"/>

        <sotaWorkflow>
            <target>CAMPAIGN</target>
            <stage>campaign_init</stage>
            <name>Campaign workflow</name>
        </sotaWorkflow>
        {{- end }}
        {{- if .Values.opts.snapstore_workflow.package_approver_enable }}
        <sotaWorkflowStage name="package_init" allowEdits="true">
            <target stage="package_approve" operation="submit" name="submit for package workflow">
                <submitters byOwner="true"/>
            </target>
        </sotaWorkflowStage>

        <sotaWorkflowStage name="package_approve">
            <target stage="package_approved" operation="approve" name="package approve it">
                <submitters>
                <role>OTA ADMIN</role>
                <role>PACKAGE APPROVER</role>
                </submitters>
            </target>
            <target stage="package_init" operation="reject" name="package deny it">
                <submitters>
                <role>OTA ADMIN</role>
                <role>PACKAGE APPROVER</role>
                </submitters>
            </target>
        </sotaWorkflowStage>

        <sotaWorkflowStage name="package_approved" published="true"/>

        <sotaWorkflow>
            <target>PACKAGE</target>
            <stage>package_init</stage>
            <name>Package workflow</name>
        </sotaWorkflow>
        {{- end }}
        <changeRequestConfiguration name="update">
            <editable>
                <name>*</name>
            </editable>
            <approvalChain name="route1">
                    <role>role2</role>
                    <role>OTA ADMIN</role>
            </approvalChain>
        </changeRequestConfiguration>
    </workflowConfiguration>
kind: ConfigMap
metadata:
  name: configmap-snapstore-workflow-xml
  namespace: {{ .Values.namespace }}

