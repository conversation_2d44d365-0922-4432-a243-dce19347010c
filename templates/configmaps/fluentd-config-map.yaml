apiVersion: v1
data:
  fluentxl4.conf: |
    <source>
      @type forward
      bind 127.0.0.1
      port {{ .Values.fluentd.port }}
    </source>
    <match **>
      @type copy
      {{- if  eq .Values.fluentd.logger.stdout.enabled true }}
      <store>
        @type stdout
      </store>
      {{- end }}
      {{- if  eq .Values.fluentd.logger.graylog.enabled true }}
      <store>
        @type gelf
        host {{ .Values.fluentd.logger.graylog.host }}
        port {{ .Values.fluentd.logger.graylog.port }}
        flush_interval {{ .Values.fluentd.logger.graylog.flush_interval }}
        <system>
          workers {{ .Values.fluentd.logger.graylog.system.workers }}
        </system>
      </store>
      {{- end }}
      {{- if  eq .Values.fluentd.logger.file.enabled true }}
      <store>
        @type file
        path {{ .Values.fluentd.logger.file.path }}
        compress gzip
        append true
        <buffer>
          time_slice_format %Y%m%d
          timekey 50m
          timekey_use_utc true
          timekey_wait 1m
          buffer_path {{ .Values.fluentd.logger.file.path }}.*
        </buffer>
      </store>
      {{- end }}
      {{- if  eq .Values.fluentd.logger.elasticSearch.enabled true }}
      <store>
        @type aws-elasticsearch-service
        type_name "eSync_Backend_Log"
        logstash_format true
        include_tag_key true
        tag_key "@log_name"
        flush_interval 1s
        <endpoint>
          url {{ .Values.fluentd.logger.elasticSearch.elastic_search_end_point }}
          region {{ .Values.fluentd.logger.elasticSearch.region }}
        </endpoint>
        <buffer>
          flush_interval {{ .Values.fluentd.logger.elasticSearch.flush_interval }}
          flush_thread_count {{ .Values.fluentd.logger.elasticSearch.flush_thread_count }}
        </buffer>
        <system>
          workers {{ .Values.fluentd.logger.elasticSearch.workers }}
        </system>
      </store>
      {{- end }}
      {{- if  eq .Values.fluentd.logger.azureblob.enabled true }}
        <store>
          @type azure-storage-append-blob
          azure_storage_account             {{ .Values.fluentd.logger.azureblob.azure_storage_account }}
          azure_storage_access_key          {{ .Values.fluentd.logger.azureblob.azure_storage_access_key }}
          azure_storage_connection_string   "{{ .Values.fluentd.logger.azureblob.azure_storage_connection_string }}"
          azure_storage_sas_token           {{ .Values.fluentd.logger.azureblob.azure_storage_sas_token }}
          azure_container                   {{ .Values.fluentd.logger.azureblob.azure_container }}
          auto_create_container             true
          path                              logs/
          azure_object_key_format           %{path}%{time_slice}_%{index}.log
          time_slice_format                 %Y%m%d-%H
          compute_checksums                 true
          <buffer>
            @type file
            path {{ .Values.fluentd.logger.azureblob.path }}
            timekey {{ .Values.fluentd.logger.azureblob.timekey }}
            timekey_wait {{ .Values.fluentd.logger.azureblob.timekey_wait }}
            timekey_use_utc true
          </buffer>
        </store>
      {{- end }}
      {{- if  eq .Values.fluentd.logger.opensearch.enabled true }}
      <store>
        @type opensearch
        logstash_format true
        include_tag_key true
      {{- if .Values.fluentd.logger.opensearch.logstash_prefix }}
        logstash_prefix {{ .Values.fluentd.logger.opensearch.logstash_prefix }}
      {{- end }}
        flush_interval {{ .Values.fluentd.logger.opensearch.flush_interval }}
        <endpoint>
          url https://{{ .Values.fluentd.logger.opensearch.open_search_end_point }}
          region {{ .Values.fluentd.logger.opensearch.region }}
          access_key_id "{{ .Values.fluentd.logger.opensearch.access_key_id }}"
          secret_access_key "{{ .Values.fluentd.logger.opensearch.secret_access_key }}"
          refresh_credentials_interval 1h
        </endpoint>
        <buffer>
          buffer_type {{ .Values.fluentd.logger.opensearch.buffer.buffer_type }}
          num_threads {{ .Values.fluentd.logger.opensearch.buffer.num_threads }}
          flush_thread_count {{ .Values.fluentd.logger.opensearch.buffer.flush_thread_count }}
          flush_interval {{ .Values.fluentd.logger.opensearch.buffer.flush_interval }}
          chunk_limit_size {{ .Values.fluentd.logger.opensearch.buffer.chunk_limit_size }}
          queue_limit_length {{ .Values.fluentd.logger.opensearch.buffer.queue_limit_length }}
          retry_max_interval {{ .Values.fluentd.logger.opensearch.buffer.retry_max_interval }}
          retry_forever {{ .Values.fluentd.logger.opensearch.buffer.retry_forever }}
        </buffer>
        <system>
          workers {{ .Values.fluentd.logger.opensearch.system.workers }}
        </system>
      </store>
      {{- end }}
      {{- if  eq .Values.fluentd.logger.s3.enabled true }}
        <store>
          @type s3
          aws_key_id "{{ .Values.fluentd.logger.s3.aws_key_id }}"
          aws_sec_key "{{ .Values.fluentd.logger.s3.aws_sec_key }}"
          s3_bucket {{ .Values.fluentd.logger.s3.s3_bucket }}
          s3_region {{ .Values.fluentd.logger.s3.s3_region }}
          path logs/
          <buffer>
            @type file
            path {{ .Values.fluentd.logger.s3.path }}
            timekey {{ .Values.fluentd.logger.s3.timekey }}
            timekey_wait {{ .Values.fluentd.logger.s3.timekey_wait }}
            chunk_limit_size {{ .Values.fluentd.logger.s3.chunk_limit_size }}
          </buffer>
          time_slice_format %Y%m%d%H
        </store>
      {{- end }}
    </match>
kind: ConfigMap
metadata:
  name: fluentd-config-map
  annotations:
{{ toYaml .Values.fluentd.annotations | indent 4 }}
  namespace: {{ .Values.namespace }}
