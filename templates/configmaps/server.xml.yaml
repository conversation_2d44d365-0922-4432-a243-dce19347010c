apiVersion: v1
data:
  server.xml: |
    <?xml version='1.0' encoding='utf-8'?>
    <Server port="8005" shutdown="SHUTDOWN">
        <Listener className="org.apache.catalina.core.AprLifecycleListener" SSLEngine="on" />
        <Listener className="org.apache.catalina.core.JreMemoryLeakPreventionListener" />
        <Listener className="org.apache.catalina.mbeans.GlobalResourcesLifecycleListener" />
        <Listener className="org.apache.catalina.core.ThreadLocalLeakPreventionListener" />
        <GlobalNamingResources>
            <Resource name="UserDatabase" auth="Container"
                type="org.apache.catalina.UserDatabase"
                description="User database that can be updated and saved"
                factory="org.apache.catalina.users.MemoryUserDatabaseFactory"
                pathname="conf/tomcat-users.xml" />
        </GlobalNamingResources>
        <Service name="Catalina">
        {{- range $k, $v := $.Values.serverxml }}
            {{- if $v.enable }}
                {{- $name := cat $k -}}
                {{- if $v.ssl }}
            <Connector port="{{ template "get-service-port" (list $name  $.Values.service )}}" protocol="org.apache.coyote.http11.Http11NioProtocol"
                maxThreads="{{ $v.port_max_threads }}" SSLEnabled="true">
                <UpgradeProtocol className="org.apache.coyote.http2.Http2Protocol"/>
                {{- if $v.truststore_jks_path }}
                        <SSLHostConfig protocols="{{ $v.protocols }}"
                        certificateVerification="{{ $v.truststore_certificate_verification }}"
                        truststorePassword="{{ $v.truststore_jks_password }}"
                        truststoreFile="{{ $v.truststore_jks_path }}">
                {{- else }}
                <SSLHostConfig protocols="{{ $v.protocols }}">
                {{- end }}
                    <Certificate certificateKeystoreFile="{{ $v.keystore_jks_path }}"
                    certificateKeystorePassword="{{ $v.certificate_keystore_password }}"
                    certificateKeyAlias="{{ $v.certificate_key_alias }}"
                    certificateKeyPassword="{{ $v.certificate_key_password }}"
                    certificateVerification="{{ $v.keystore_certificate_verification }}"  type="RSA" />
                </SSLHostConfig>
            </Connector>
                {{- else }}
            <Connector port="{{ template "get-service-port" (list $name  $.Values.service )}}" protocol="org.apache.coyote.http11.Http11NioProtocol"
                maxThreads="{{ $v.port_max_threads }}" connectionTimeout="20000">
                <UpgradeProtocol className="org.apache.coyote.http2.Http2Protocol"
                compression="on"
                compressibleMimeType="text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json,application/vnd.syncml.dm+xml"/>
            </Connector>
                {{- end }}
            {{- end }}
        {{- end }}     
            <Engine name="Catalina" defaultHost="localhost">
                <Realm className="org.apache.catalina.realm.LockOutRealm">
                    <Realm className="org.apache.catalina.realm.UserDatabaseRealm"
                resourceName="UserDatabase"/></Realm>
                <Host name="localhost"  appBase="webapps"
                unpackWARs="true" autoDeploy="true">
                <Valve className="org.apache.catalina.valves.ErrorReportValve" showReport="false" showServerInfo="false"/>
                <Valve className="org.apache.catalina.valves.rewrite.RewriteValve" />
                <Valve className="org.excelfore.tomcat.valve.acl.TomcatValve" config="conf/acl-device.xml"/>
                    <!-- Access log processes all example.
                Documentation at: /docs/config/valve.html
                Note: The pattern used is equivalent to using pattern="common" -->
                    <!--Valve className="org.apache.catalina.valves.AccessLogValve" directory="logs"
                prefix="localhost_access_log" suffix=".txt"
            pattern="%h %l %u %t &quot;%r&quot; %s %b" /> -->
                    <Context docBase="/usr/local/tomcat/conf/static" path="static/" />
                </Host>
                <Cluster className="org.apache.catalina.ha.tcp.SimpleTcpCluster"
                channelSendOptions="8">
                    <Manager className="org.apache.catalina.ha.session.DeltaManager"
                    expireSessionsOnShutdown="false"
                    notifyListenersOnReplication="true"
                    stateTransferTimeout="120"/>
                    <Channel className="org.apache.catalina.tribes.group.GroupChannel">
                        <Membership className="org.apache.catalina.tribes.membership.cloud.CloudMembershipService"
                                    membershipProviderClassName="kubernetes"
                        />
                        <!--Membership className="org.apache.catalina.tribes.membership.McastService"
                        address="*********"
                        port="45564"
                        frequency="500"
                        dropTime="3000"/-->
                        <Receiver className="org.apache.catalina.tribes.transport.nio.NioReceiver"
                        address="auto"
                        port="4000"
                        autoBind="100"
                        selectorTimeout="5000"
                        maxThreads="6"/>
                        <Sender className="org.apache.catalina.tribes.transport.ReplicationTransmitter">
                            <Transport className="org.apache.catalina.tribes.transport.nio.PooledParallelSender"/>
                        </Sender>
                        <Interceptor className="org.apache.catalina.tribes.group.interceptors.TcpFailureDetector"/>
                        <Interceptor className="org.apache.catalina.tribes.group.interceptors.MessageDispatchInterceptor"/>
                    </Channel>
                    <Valve className="org.apache.catalina.ha.tcp.ReplicationValve" filter=""/>
                    <Valve className="org.apache.catalina.ha.session.JvmRouteBinderValve"/>
                    <ClusterListener className="org.apache.catalina.ha.session.ClusterSessionListener"/>
                </Cluster>
            </Engine>
        </Service>
    </Server>

kind: ConfigMap
metadata:
  name: configmap-server-xl4
  namespace: {{ .Values.namespace }}
