apiVersion: v1
data:
  demo_device_groups.xml: |
    <?xml version="1.0" encoding="UTF-8" standalone="yes"?>

    <d:demo-device-groups
            xmlns:d="https://dev-esync.excelfore.com/schema/xsd/demo-device-groups.xsd"
            xmlns:xs="http://www.w3.org/2001/XMLSchema-instance"
    >

        <d:group-router>
            <d:attribute-definition attribute="all-devices" source="true"/>
            <d:attribute-definition attribute="for-role6" source="true"/>
            <d:device-filter xs:type="d:deviceAttrRegex" name="VID" regex=".*"/>
        </d:group-router>

        <d:group-router>
            <d:attribute-definition attribute="isMake" source="true"/>
            <d:attribute-definition attribute="make" source="${MAKE}"/>
            <d:attribute-definition attribute="for-role6" source="true"/>
            <d:device-filter xs:type="d:deviceAttrRegex" name="VID" regex=".*"/>
        </d:group-router>

        <d:group-router>
            <d:attribute-definition attribute="isModel" source="true"/>
            <d:attribute-definition attribute="make" source="${MAKE}"/>
            <d:attribute-definition attribute="model" source="${MODEL}"/>
            <d:attribute-definition attribute="for-role6" source="true"/>
            <d:device-filter xs:type="d:deviceAttrRegex" name="VID" regex=".*"/>
        </d:group-router>

        <d:group-router>
            <d:attribute-definition attribute="isYear" source="true"/>
            <d:attribute-definition attribute="make" source="${MAKE}"/>
            <d:attribute-definition attribute="model" source="${MODEL}"/>
            <d:attribute-definition attribute="year" source="${YEAR}"/>
            <d:attribute-definition attribute="for-role6" source="true"/>
            <d:device-filter xs:type="d:deviceAttrRegex" name="VID" regex=".*"/>
        </d:group-router>

        <d:group-router>
            <d:attribute-definition attribute="isLocation" source="true"/>
            <d:attribute-definition attribute="make" source="${MAKE}"/>
            <d:attribute-definition attribute="model" source="${MODEL}"/>
            <d:attribute-definition attribute="year" source="${YEAR}"/>
            <d:attribute-definition attribute="location" source="${LOCATION}"/>
            <d:attribute-definition attribute="for-role6" source="true"/>
            <d:device-filter xs:type="d:deviceAttrRegex" name="VID" regex=".*"/>
        </d:group-router>

        <d:access-control>
            <d:role>OTA ADMIN</d:role>
            <d:type>UPDATE</d:type>
            <d:expression xs:type="d:stringAttrRegex" name="for-role6" regex="true"/>
        </d:access-control>

        <d:access-control>
            <d:role>OTA ADMIN</d:role>
            <d:type>INFORMATION</d:type>
            <d:expression xs:type="d:stringAttrRegex" name="for-role6" regex="true"/>
        </d:access-control>

    </d:demo-device-groups>

kind: ConfigMap
metadata:
  name: configmap-demo-device-groups-config
  namespace: {{ .Values.namespace }}
