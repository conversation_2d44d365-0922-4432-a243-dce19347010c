apiVersion: v1
data:
  acl-device.xml: |
    <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
        <d:tomcat-valve-request
                xmlns:d="https://dev-esync.excelfore.com/schema/public/xsd/1.0/tomcat-valve-request.xsd"
                xmlns:x="http://www.w3.org/2001/XMLSchema-instance" connector="{{ .Values.service.endpoints.oma.port }}">

            <d:outcome x:type="d:SendError" code="404"/>
            <d:match pattern="^\/snap\/oma$">
                <d:outcome x:type="d:Continue"/>
            </d:match>
            <d:match pattern="^\/snap\/oma\/.*$">
                <d:outcome x:type="d:Continue"/>
            </d:match>
            <d:match pattern="^\/snap\/odl\/.*$">
                <d:outcome x:type="d:Continue"/>
            </d:match>
            <d:match pattern="^\/snap\/dmclient\/.*$">
                <d:outcome x:type="d:Continue"/>
            </d:match>
        </d:tomcat-valve-request>

kind: ConfigMap
metadata:
  name: configmap-acl-device-xml
  namespace: {{ .Values.namespace }}