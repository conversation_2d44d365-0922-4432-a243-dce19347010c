{{- if .Values.istio.enabled -}}
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: {{ include "esync-server-chart.fullname" . }}-ui
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: {{ .Values.service.endpoints.snap.port }}
      name: http-esync-ui
      protocol: TCP
    hosts:
    - {{ .Values.domain }}
---
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: {{ include "esync-server-chart.fullname" . }}-device
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: {{ .Values.service.endpoints.oma.port }}
      name: http-esync-device
      protocol: TCP
    hosts:
    - {{ .Values.domain }}
---
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: {{ include "esync-server-chart.fullname" . }}-tsp
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: {{ .Values.service.endpoints.tsp.port }}
      name: http-esync-tsp
      protocol: TCP
    hosts:
    - {{ .Values.domain }}
{{- end }}
