{{- if .Values.networkPolicy.enabled }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "esync-server-chart.fullname" . }}
  namespace: {{ .Values.namespace }}
  {{- with .Values.networkPolicy.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }} 
spec:
  podSelector:
    matchLabels:
      {{- include "esync-server-chart.matchLabels" . | nindent 6 }}
  policyTypes:
  - Ingress      
  ingress:
  - from:
    - ipBlock:
        cidr: {{ .Values.networkPolicy.ipBlock.cidr }}
        except: {{ .Values.networkPolicy.ipBlock.except_cidr }}
{{- range .Values.networkPolicy.namespaceSelector }}
    - namespaceSelector:      
        matchLabels:
          {{.name }}: {{ .value }}
{{- end }}
{{- range .Values.networkPolicy.podSelector }}
    - podSelector:      
        matchLabels:
          {{.name }}: {{ .value }}
{{- end }}        
{{- end }}