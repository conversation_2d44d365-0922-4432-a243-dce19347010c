kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ include "esync-server-chart.fullname" . }}
  {{- with .Values.rbac.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}   
subjects:
- kind: ServiceAccount
  name: {{ include "esync-server-chart.serviceAccountName" . }}
  namespace: {{ .Values.namespace }}        
roleRef:
  kind: ClusterRole
  name: {{ include "esync-server-chart.fullname" . }}
  apiGroup: rbac.authorization.k8s.io