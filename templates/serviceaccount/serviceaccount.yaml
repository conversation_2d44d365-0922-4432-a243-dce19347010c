{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "esync-server-chart.serviceAccountName" . }}
  annotations:
  {{- with .Values.serviceAccount.annotations }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "esync-server-chart.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
