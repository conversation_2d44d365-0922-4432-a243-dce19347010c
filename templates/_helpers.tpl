{{/*
Expand the name of the chart.
*/}}
{{- define "esync-server-chart.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "esync-server-chart.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "esync-server-chart.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "esync-server-chart.labels" -}}
helm.sh/chart: {{ include "esync-server-chart.chart" . }}
{{ include "esync-server-chart.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "esync-server-chart.selectorLabels" -}}
app.kubernetes.io/name: {{ include "esync-server-chart.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
app: {{ include "esync-server-chart.name" . }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "esync-server-chart.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "esync-server-chart.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Create chart matchLabels
*/}}
{{- define "esync-server-chart.matchLabels" -}}
app: esync-server-chart
{{- end -}}


{{/*
Create the imagePullSecret to use
*/}}
{{- define "imagePullSecret" }}
{{- printf "{\"auths\": {\"%s\": {\"auth\": \"%s\"}}}" .Values.imageCredentials.registry (printf "%s:%s" .Values.imageCredentials.username .Values.imageCredentials.password | b64enc) | b64enc }}
{{- end }}

{{/*
Create the custom_javaopts_configurations
*/}}
{{- define "custom_javaopts_configurations" -}}
{{- join " " .Values.opts.custom_javaopts_configurations }}
{{- end -}}

{{/*
Create the custom_catalinaopts_configurations
*/}}
{{- define "custom_catalinaopts_configurations" -}}
{{- join " " .Values.opts.custom_catalinaopts_configurations.parameters }}
{{- end -}}

{{/*
Create the custom_otelopts_configurations
*/}}
{{- define "custom_otelopts_configurations" -}}
{{- join " " .Values.custom_otelopts_configurations }}
{{- end -}}


{{- define "server-dns-name" -}}
{{- if .Values.domain -}}
{{- .Values.domain | trunc 50 | trimSuffix "-" -}}
{{- end -}}
{{- end -}}

{{/*
Create the snap_url_generator
*/}}
{{- define "snap-url-generator" -}}
{{- if eq (int .Values.service.endpoints.snap.port) 443 }}
{{- printf  " https://%s/snap/" .Values.domain }}
{{- else if eq (int .Values.service.endpoints.snap.port) 80}}
{{- printf  " http://%s/snap/" .Values.domain }}
{{- else }}
{{- $snapport := cat $.Values.service.endpoints.snap.port -}}
{{- printf " %s://%s:%s/snap/" .Values.snapstore.snap_url_protocol (include "server-dns-name" .) $snapport -}}
{{- end }}
{{- end }}

{{/*
Create the custom_snapstore_config
*/}}
{{- define "custom_snapstore_config_key" -}}
{{- range $v := .Values.opts.custom_snapstore_configurations }}
     {{- $arg1 := $v.key -}}
     {{- $arg2 := cat $v.value -}}
    {{- printf "%s = %s" $arg1 $arg2 | trim | nindent 4 }}
{{- end }} 
{{- end -}}

{{/*
Create the redis configuration
*/}}
{{- define "redis-configuration-generator" -}}
{{- if  eq .Values.redis.enabled true }}
    snapstore.redis.enabled = true
    {{- $direct := cat $.Values.redis.direct -}}
    {{- printf  " snapstore.redis.direct = %s" $direct | trim | nindent 4 }}
    {{- $port := cat $.Values.redis.port -}}
    {{- printf  " snapstore.redis.uri_st = redis://%s:%s" .Values.redis.endpoint_url $port | trim | nindent 4 }}
{{- else}}
    snapstore.redis.enabled = false
{{- end }}
{{- end }}

{{/*
Get the service port number
*/}}

{{- define "get-service-port" -}}
    {{- $name := index . 0 -}}
    {{- $service := index . 1 -}}
    {{- range $k, $v := $service.endpoints }}
        {{- if  eq $k $name }}
            {{- $port := cat $v.targetPort -}}
            {{- printf  "%s" $port | trim  }}
        {{- end }}
    {{- end }}
{{- end }}

{{/*
Add new line
*/}}
{{- define "add_new_line" -}}
    {{- $emptyline := cat "" -}}
    {{- printf  "%s" $emptyline | trim | nindent 2 }}
{{- end -}}

{{/*
Create the custom_configjson_configurations
*/}}
{{- define "custom_configjson_configurations" -}}
{{- if eq  .Values.opts.custom_configjson_configurations.enable true }}
{{- .Values.opts.custom_configjson_configurations.configs | toPrettyJson | indent 6 | trimSuffix "}" | trim | trimPrefix "{" }},
{{- end }}
{{- end -}}