apiVersion: v1
kind: Service
metadata:
  annotations:
   namespace: {{ .Values.namespace }}
   {{- if .Values.service.loadBalancerTimeOutInSeconds }}
   service.beta.kubernetes.io/aws-load-balancer-connection-idle-timeout: "{{ .Values.service.loadBalancerTimeOutInSeconds }}"
   {{- end }}
   {{- if .Values.service.awsLoadBalancerSslCert }}
   {{- if .Values.service.internal }}
   service.beta.kubernetes.io/aws-load-balancer-internal: "{{ .Values.service.internal }}"
   {{- end }}
   service.beta.kubernetes.io/aws-load-balancer-backend-protocol: tcp
   service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
   service.beta.kubernetes.io/aws-load-balancer-ssl-cert: {{ .Values.service.awsLoadBalancerSslCert }}
   service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "{{ .Values.service.endpoints.snap.port }}"
   service.beta.kubernetes.io/aws-load-balancer-target-node-labels: "worker=esync-server"
   {{- end }}
   {{- with .Values.service.annotations }}
    {{- toYaml . | nindent 2 }}
   {{- end }}
  namespace: {{ .Values.namespace }}  
  name: {{ include "esync-server-chart.fullname" . }}
  labels:
    {{- include "esync-server-chart.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range $k, $v := .Values.service.endpoints }}
    - port: {{ $v.port }}
      targetPort: {{ $v.targetPort }}
      protocol: {{ $v.protocol }}
      name: {{ $k }}
    {{- if $v.nodePort }}
      nodePort: {{ $v.nodePort }}
    {{- end }}
    {{- end }}      
  selector:
    {{- include "esync-server-chart.selectorLabels" . | nindent 4 }}