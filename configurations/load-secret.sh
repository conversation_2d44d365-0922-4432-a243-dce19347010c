#!/bin/bash -e

usage()
{
cat <<USAGE
Usage Options :
load-config-map-secret.sh
	 --create-secrets <namespace>      # To create secrets
	 --reload-secrets	            # To delete and recreate the secrets
E.g $ ./load-config-map-secret.sh --reload-secrets	

USAGE
}

get_arg ()
{
	read -e -r tmp
	echo $tmp
}

create_secrets ()
{
if [[ $# -eq 0 ]]; then
	echo "Error: Please enter namespace name. Eg ./load-config-map-secret.sh --create-secrets ghi-test /home/<USER>/Deployments/clusters/qa-servers/core-ci-qa"
	exit 1
else
	namespace=$1
	dep_path=$2
	export CERTS_PATH=""
	FILES=$(find $dep_path/pki -maxdepth 1 -type f -name "*.jks" -o -name "*.pem")
	for f in $FILES
	do
	  CERTS_PATH="$CERTS_PATH --from-file=$f "
	done
	FINAL_COMMAND="kubectl create secret generic esync-certificate $CERTS_PATH -n $namespace"
	echo $FINAL_COMMAND
	eval $FINAL_COMMAND
fi
}

delete_secrets ()
{
	namespace=$1
	kubectl delete secret --ignore-not-found esync-certificate -n $namespace
}

reload_secrets ()
{
	printf "Enter namespace name: "
	namespace=$(get_arg)
	printf "Enter chart name: "
	chart=$(get_arg)
	printf "Enter deployment path ( eg. /home/<USER>/opt/soft/appshack ): "
	dep_path=$(get_arg)
	delete_secrets $namespace
	create_secrets $namespace $dep_path
	cd $PWD/..
	helm upgrade --install $chart --namespace $namespace ./
	kubectl patch deployment.apps/$chart-esync-server-chart -n $namespace -p '{"spec":{"template":{"metadata":{"annotations":{"date":"'$(date +%s)'"}}}}}'
}

############## MAIN ##################

if [[ $# -eq 0 ]]; then
    echo "[Error !]: Input 'option' required to proceed(e.g., $0 -c )! "; usage; exit 1
fi

export USER_INPUT=$1
export namespace=$2
export dep_path=$3

case "$USER_INPUT" in
--reload-secrets)
          reload_secrets;
;;
--create-secrets)
          create_secrets $namespace $dep_path;
;;

*)  echo "[Error !]: Invalid option: '$1'"
          usage;
          exit 1
esac

