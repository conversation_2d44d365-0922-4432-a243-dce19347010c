#!/bin/bash -e
if [[ $# -le 1 ]]; then
        echo "Error: Please enter namespace name and deployment path. Eg ./configure.sh <namespace> <path to helm folder containing pki file>"
        exit 1
fi

NAMESPACE_NAME=${1:-default};

NS=$(kube<PERSON>l get namespace $NAMESPACE_NAME --ignore-not-found);
if [[ "$NS" ]]; then
  echo "Skipping creation of namespace $NAMESPACE_NAME - already exists";
else
  echo "Creating namespace $NAMESPACE_NAME";
  kubectl create namespace $NAMESPACE_NAME;
fi;

dep_path=$2
echo "Deployment Path: $dep_path"

bash -x load-secret.sh --create-secrets $NAMESPACE_NAME $dep_path